# Advanced Database Design for Food Ordering Application

## Overview

This document outlines the enhanced database design that includes advanced features for food types, meal times, multiple categories, and comprehensive cuisine management.

## New Features Implemented

### 1. Food Types Enhancement
- **Old**: Simple `is_vegetarian` boolean field
- **New**: `food_type` enum with values: `'veg'`, `'non_veg'`
- **Benefits**: More explicit and extensible food classification

### 2. Meal Times System
- **New Table**: `meal_times`
- **Relationship**: Many-to-many with food items via `food_item_meal_times`
- **Features**:
  - Breakfast, Lunch, Dinner, Tea/Snacks categorization
  - Time-based availability (start_time, end_time)
  - Primary meal time designation for each food item

### 3. Multiple Categories Support
- **Old**: Single category per food item (`category_id`)
- **New**: Many-to-many relationship via `food_item_categories`
- **Features**:
  - Food items can belong to multiple categories
  - Primary category designation
  - Sort ordering for category display

### 4. Enhanced Cuisines System
- **Old**: Single cuisine per food item (`cuisine_id`)
- **New**: Many-to-many relationship via `food_item_cuisines`
- **Additional**: `cuisine_categories` table for cuisine-specific categories
- **Features**:
  - Food items can belong to multiple cuisines
  - Primary cuisine designation
  - Cuisines can have their own category systems

## Database Tables

### Core Tables
1. `meal_times` - Meal time definitions
2. `food_item_meal_times` - Food item to meal time relationships
3. `food_item_categories` - Food item to category relationships
4. `food_item_cuisines` - Food item to cuisine relationships
5. `cuisine_categories` - Cuisine to category relationships

### Modified Tables
- `food_items`: Added `food_type` enum, removed `is_vegetarian`, `category_id`, `cuisine_id`

## Model Relationships

### FoodItem Model
```php
// Many-to-many relationships
public function categories(): BelongsToMany
public function cuisines(): BelongsToMany
public function mealTimes(): BelongsToMany

// Backward compatibility (if needed)
public function primaryCategory(): BelongsTo
public function primaryCuisine(): BelongsTo
```

### Category Model
```php
public function foodItems(): BelongsToMany
public function cuisines(): BelongsToMany
```

### Cuisine Model
```php
public function foodItems(): BelongsToMany
public function categories(): BelongsToMany
```

### MealTime Model
```php
public function foodItems(): BelongsToMany
public function activeFoodItems(): BelongsToMany
```

## Usage Examples

### Filtering Food Items

```php
// By food type
$vegItems = FoodItem::vegetarian()->get();
$nonVegItems = FoodItem::nonVegetarian()->get();
$specificType = FoodItem::byFoodType('veg')->get();

// By meal time
$breakfastItems = FoodItem::byMealTime(1)->get();
$multiMealItems = FoodItem::byMealTimes([1, 2])->get();

// By categories
$categoryItems = FoodItem::byCategory(1)->get();
$multiCategoryItems = FoodItem::byCategories([1, 2, 3])->get();

// By cuisines
$cuisineItems = FoodItem::byCuisine(1)->get();
$multiCuisineItems = FoodItem::byCuisines([1, 2])->get();

// Complex filtering
$items = FoodItem::vegetarian()
    ->byMealTime(1)
    ->byCategories([1, 2])
    ->available()
    ->get();
```

### Creating Food Items with Relationships

```php
$foodItem = FoodItem::create([
    'name' => 'Masala Dosa',
    'food_type' => 'veg',
    'price_per_unit' => 120.00,
    // ... other fields
]);

// Attach categories
$foodItem->categories()->attach([
    1 => ['is_primary' => true, 'sort_order' => 0],
    2 => ['is_primary' => false, 'sort_order' => 1],
]);

// Attach cuisines
$foodItem->cuisines()->attach([
    1 => ['is_primary' => true, 'sort_order' => 0],
]);

// Attach meal times
$foodItem->mealTimes()->attach([
    1 => ['is_primary' => true, 'sort_order' => 0],
    2 => ['is_primary' => false, 'sort_order' => 1],
]);
```

## Migration Instructions

1. **Run Migrations**:
   ```bash
   php artisan migrate
   ```

2. **Seed Data**:
   ```bash
   php artisan db:seed --class=MealTimeSeeder
   php artisan db:seed --class=AdvancedFoodSystemSeeder
   ```

3. **Data Migration**: The migration automatically handles:
   - Converting `is_vegetarian` to `food_type`
   - Moving single relationships to many-to-many pivot tables
   - Preserving existing data integrity

## Frontend Integration

### Filter Components
- **Horizontal Sliders**: Premium/Classic/Delux tiers
- **Secondary Row**: Veg/Non-veg buttons
- **Meal Time Filters**: Breakfast, Lunch, Dinner, Tea/Snacks
- **Category Filters**: Left sticky slider for subcategories
- **Cuisine Filters**: Dropdown or slider for cuisine selection

### Mobile Responsive Design
- Maintains existing mobile-first responsive design
- Sticky filter bars positioned below navigation
- Left sidebar for advanced filtering (1/6 screen width)
- Single row layout for food items on mobile

## Benefits

1. **Flexibility**: Food items can belong to multiple categories and cuisines
2. **Meal Planning**: Time-based food categorization
3. **Better Filtering**: More granular search and filter options
4. **Scalability**: Easy to add new meal times, categories, and cuisines
5. **User Experience**: Enhanced navigation with hierarchical filtering
6. **Data Integrity**: Proper normalization with maintained relationships

## Backward Compatibility

The system maintains backward compatibility through:
- Migration scripts that preserve existing data
- Helper methods for single category/cuisine access
- Gradual transition support for existing frontend code
