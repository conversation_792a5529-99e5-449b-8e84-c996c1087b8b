@extends('admin.layouts.app')

@section('title', 'Inventory Report')
@section('page-title', 'Inventory Report')

@section('page-actions')
<div class="btn-group" role="group">
    <a href="{{ route('admin.reports.index') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Reports
    </a>
    <a href="{{ route('admin.food-items.index') }}" class="btn btn-outline-primary">
        <i class="bi bi-egg-fried me-1"></i>
        Manage Food Items
    </a>
</div>
@endsection

@section('content')
<!-- Alert Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="bi bi-exclamation-triangle text-warning fs-1"></i>
                <h4 class="text-warning">{{ $lowStockItems->count() }}</h4>
                <p class="text-muted mb-0">Low Stock Items</p>
                <small class="text-muted">≤ 10 units</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-danger">
            <div class="card-body text-center">
                <i class="bi bi-x-circle text-danger fs-1"></i>
                <h4 class="text-danger">{{ $outOfStockItems->count() }}</h4>
                <p class="text-muted mb-0">Out of Stock</p>
                <small class="text-muted">unavailable items</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="bi bi-fire text-success fs-1"></i>
                <h4 class="text-success">{{ $popularItems->take(5)->count() }}</h4>
                <p class="text-muted mb-0">Top Sellers</p>
                <small class="text-muted">most popular</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="bi bi-grid text-info fs-1"></i>
                <h4 class="text-info">{{ $categoryDistribution->count() }}</h4>
                <p class="text-muted mb-0">Categories</p>
                <small class="text-muted">active categories</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Low Stock Items -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h6 class="mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Low Stock Items
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Item</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($lowStockItems as $item)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($item->image)
                                                <img src="{{ Storage::url($item->image) }}" alt="{{ $item->name }}" 
                                                     class="rounded me-3" style="width: 40px; height: 40px; object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="bi bi-image text-muted"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="fw-bold">{{ $item->name }}</div>
                                                <small class="text-muted">{{ $item->category->name ?? 'N/A' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $item->stock_quantity <= 5 ? 'danger' : 'warning' }}">
                                            {{ $item->stock_quantity }} {{ $item->unit }}s
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $item->is_available ? 'success' : 'secondary' }}">
                                            {{ $item->is_available ? 'Available' : 'Unavailable' }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.food-items.show', $item) }}" class="btn btn-sm btn-outline-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.food-items.edit', $item) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <div class="text-success">
                                            <i class="bi bi-check-circle fs-3"></i>
                                            <p class="mt-2 mb-0">All items are well stocked!</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Out of Stock Items -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">
                    <i class="bi bi-x-circle me-2"></i>
                    Out of Stock Items
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Item</th>
                                <th>Reason</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($outOfStockItems as $item)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($item->image)
                                                <img src="{{ Storage::url($item->image) }}" alt="{{ $item->name }}" 
                                                     class="rounded me-3" style="width: 40px; height: 40px; object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="bi bi-image text-muted"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="fw-bold">{{ $item->name }}</div>
                                                <small class="text-muted">{{ $item->category->name ?? 'N/A' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if($item->stock_quantity == 0)
                                            <span class="badge bg-danger">No Stock</span>
                                        @else
                                            <span class="badge bg-secondary">Disabled</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.food-items.show', $item) }}" class="btn btn-sm btn-outline-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.food-items.edit', $item) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="3" class="text-center py-4">
                                        <div class="text-success">
                                            <i class="bi bi-check-circle fs-3"></i>
                                            <p class="mt-2 mb-0">All items are in stock!</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Popular Items -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="bi bi-fire me-2"></i>
            Most Popular Items
        </h6>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Rank</th>
                        <th>Item</th>
                        <th>Category</th>
                        <th>Orders</th>
                        <th>Stock Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($popularItems as $index => $item)
                        <tr>
                            <td>
                                <span class="badge bg-{{ $index < 3 ? 'warning' : 'secondary' }} fs-6">
                                    #{{ $index + 1 }}
                                </span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($item->image)
                                        <img src="{{ Storage::url($item->image) }}" alt="{{ $item->name }}" 
                                             class="rounded me-3" style="width: 40px; height: 40px; object-fit: cover;">
                                    @else
                                        <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="bi bi-image text-muted"></i>
                                        </div>
                                    @endif
                                    <div>
                                        <div class="fw-bold">{{ $item->name }}</div>
                                        <small class="text-muted">${{ number_format($item->price_per_unit, 2) }} per {{ $item->unit }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $item->category->name ?? 'N/A' }}</span>
                            </td>
                            <td>
                                <span class="badge bg-primary fs-6">{{ $item->order_items_count }}</span>
                            </td>
                            <td>
                                @if($item->stock_quantity !== null)
                                    <span class="badge bg-{{ $item->stock_quantity > 10 ? 'success' : ($item->stock_quantity > 0 ? 'warning' : 'danger') }}">
                                        {{ $item->stock_quantity }} {{ $item->unit }}s
                                    </span>
                                @else
                                    <span class="badge bg-success">Unlimited</span>
                                @endif
                            </td>
                            <td>
                                <a href="{{ route('admin.food-items.show', $item) }}" class="btn btn-sm btn-outline-info">
                                    <i class="bi bi-eye"></i>
                                </a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">No order data available</div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Category Distribution -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="bi bi-grid me-2"></i>
            Category Distribution
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <canvas id="categoryChart" height="150"></canvas>
            </div>
            <div class="col-md-4">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Items</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($categoryDistribution as $category)
                                <tr>
                                    <td>{{ $category->name }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ $category->food_items_count }}</span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Category Distribution Chart
const categoryCtx = document.getElementById('categoryChart').getContext('2d');
const categoryChart = new Chart(categoryCtx, {
    type: 'doughnut',
    data: {
        labels: [
            @foreach($categoryDistribution as $category)
                '{{ $category->name }}',
            @endforeach
        ],
        datasets: [{
            data: [
                @foreach($categoryDistribution as $category)
                    {{ $category->food_items_count }},
                @endforeach
            ],
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40',
                '#FF6384',
                '#C9CBCF'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': ' + context.parsed + ' items';
                    }
                }
            }
        }
    }
});
</script>
@endpush
