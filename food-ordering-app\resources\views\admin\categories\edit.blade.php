@extends('admin.layouts.app')

@section('title', 'Edit Category')
@section('page-title', 'Edit Category: ' . $category->name)

@section('page-actions')
<div class="d-flex gap-2">
    <a href="{{ route('admin.categories.show', $category) }}" class="btn btn-outline-info">
        <i class="bi bi-eye me-1"></i>
        View Category
    </a>
    <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Categories
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Category Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.categories.update', $category) }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name', $category->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">Parent Category</label>
                                <select class="form-select @error('parent_id') is-invalid @enderror"
                                        id="parent_id" name="parent_id">
                                    <option value="">Main Category (No Parent)</option>
                                    @foreach($mainCategories as $mainCategory)
                                        @if($mainCategory->id !== $category->id)
                                            <option value="{{ $mainCategory->id }}"
                                                {{ old('parent_id', $category->parent_id) == $mainCategory->id ? 'selected' : '' }}>
                                                {{ $mainCategory->name }}
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                                @error('parent_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Leave empty to make this a main category</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', $category->sort_order) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Lower numbers appear first</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Category Type</label>
                                <div class="form-control-plaintext">
                                    <span id="category-type-text" class="badge bg-{{ $category->isMainCategory() ? 'primary' : 'success' }}">
                                        {{ $category->isMainCategory() ? 'Main Category' : 'Subcategory' }}
                                    </span>
                                    @if($category->isSubcategory())
                                        <small class="text-muted d-block">Parent: {{ $category->parent->name }}</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3">{{ old('description', $category->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">Category Image</label>
                        @if($category->image)
                            <div class="mb-2">
                                <img src="{{ Storage::url($category->image) }}" alt="{{ $category->name }}" 
                                     class="rounded" width="100" height="75" style="object-fit: cover;">
                                <div class="form-text">Current image</div>
                            </div>
                        @endif
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">
                            Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB
                            @if($category->image)
                                <br>Leave empty to keep current image
                            @endif
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                   value="1" {{ old('is_active', $category->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                        <div class="form-text">Inactive categories won't be visible to customers</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>
                            Update Category
                        </button>
                        <a href="{{ route('admin.categories.show', $category) }}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Category Statistics</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="fs-4 fw-bold text-primary">{{ $category->foodItems()->count() }}</div>
                            <div class="small text-muted">Food Items</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="fs-4 fw-bold text-success">{{ $category->packages()->count() }}</div>
                        <div class="small text-muted">Packages</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Category Details</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="row mb-2">
                        <div class="col-5 text-muted">Slug:</div>
                        <div class="col-7">{{ $category->slug }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5 text-muted">Created:</div>
                        <div class="col-7">{{ $category->created_at->format('M d, Y') }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5 text-muted">Updated:</div>
                        <div class="col-7">{{ $category->updated_at->format('M d, Y') }}</div>
                    </div>
                    <div class="row">
                        <div class="col-5 text-muted">Status:</div>
                        <div class="col-7">
                            <span class="badge bg-{{ $category->is_active ? 'success' : 'secondary' }}">
                                {{ $category->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Preview</h6>
            </div>
            <div class="card-body">
                <div id="categoryPreview" class="text-center">
                    <div class="bg-light rounded mb-2" style="height: 120px; display: flex; align-items: center; justify-content: center;">
                        @if($category->image)
                            <img src="{{ Storage::url($category->image) }}" alt="{{ $category->name }}" 
                                 style="width: 100%; height: 120px; object-fit: cover;" class="rounded">
                        @else
                            <i class="bi bi-image text-muted fs-1"></i>
                        @endif
                    </div>
                    <div class="fw-bold" id="previewName">{{ $category->name }}</div>
                    <div class="text-muted small" id="previewDescription">{{ $category->description ?: 'No description' }}</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Live preview functionality
document.getElementById('name').addEventListener('input', function() {
    document.getElementById('previewName').textContent = this.value || '{{ $category->name }}';
});

document.getElementById('description').addEventListener('input', function() {
    document.getElementById('previewDescription').textContent = this.value || 'No description';
});

// Image preview
document.getElementById('image').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.querySelector('#categoryPreview .bg-light, #categoryPreview img').parentElement;
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" style="width: 100%; height: 120px; object-fit: cover;" class="rounded">`;
        };
        reader.readAsDataURL(file);
    }
});
</script>
@endpush
