<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\CateringOrder;
use App\Models\User;
use App\Models\FoodItem;
use App\Models\Category;
use App\Models\Cuisine;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Display the main reports dashboard.
     */
    public function index()
    {
        // Key metrics
        $totalRevenue = Order::where('payment_status', 'paid')->sum('total_amount') + 
                       CateringOrder::where('payment_status', 'paid')->sum('total_amount');
        
        $totalOrders = Order::count() + CateringOrder::count();
        $totalCustomers = User::where('role', 'customer')->count();
        $totalFoodItems = FoodItem::count();

        // This month vs last month comparison
        $thisMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();
        
        $thisMonthRevenue = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', $thisMonth)
            ->sum('total_amount') +
            CateringOrder::where('payment_status', 'paid')
            ->where('created_at', '>=', $thisMonth)
            ->sum('total_amount');
            
        $lastMonthRevenue = Order::where('payment_status', 'paid')
            ->whereBetween('created_at', [$lastMonth, $thisMonth])
            ->sum('total_amount') +
            CateringOrder::where('payment_status', 'paid')
            ->whereBetween('created_at', [$lastMonth, $thisMonth])
            ->sum('total_amount');

        $revenueGrowth = $lastMonthRevenue > 0 ? (($thisMonthRevenue - $lastMonthRevenue) / $lastMonthRevenue) * 100 : 0;

        // Order trends
        $thisMonthOrders = Order::where('created_at', '>=', $thisMonth)->count() +
                          CateringOrder::where('created_at', '>=', $thisMonth)->count();
        
        $lastMonthOrders = Order::whereBetween('created_at', [$lastMonth, $thisMonth])->count() +
                          CateringOrder::whereBetween('created_at', [$lastMonth, $thisMonth])->count();

        $orderGrowth = $lastMonthOrders > 0 ? (($thisMonthOrders - $lastMonthOrders) / $lastMonthOrders) * 100 : 0;

        // Top performing items
        $topFoodItems = FoodItem::withCount(['orderItems'])
            ->withSum('orderItems', 'total_price')
            ->orderBy('order_items_count', 'desc')
            ->limit(10)
            ->get();

        // Recent activity
        $recentOrders = Order::with(['user'])
            ->latest()
            ->limit(5)
            ->get();

        $recentCateringOrders = CateringOrder::with(['eventType'])
            ->latest()
            ->limit(5)
            ->get();

        return view('admin.reports.index', compact(
            'totalRevenue',
            'totalOrders',
            'totalCustomers',
            'totalFoodItems',
            'revenueGrowth',
            'orderGrowth',
            'topFoodItems',
            'recentOrders',
            'recentCateringOrders'
        ));
    }

    /**
     * Sales report.
     */
    public function sales(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));
        $groupBy = $request->get('group_by', 'day'); // day, week, month

        // Sales data
        $salesData = $this->getSalesData($dateFrom, $dateTo, $groupBy);
        
        // Category performance
        $categoryPerformance = $this->getCategoryPerformance($dateFrom, $dateTo);
        
        // Payment method breakdown
        $paymentMethods = Order::whereBetween('created_at', [$dateFrom, $dateTo])
            ->select('payment_method', DB::raw('count(*) as count'), DB::raw('SUM(total_amount) as revenue'))
            ->groupBy('payment_method')
            ->get();

        // Order type breakdown
        $orderTypes = Order::whereBetween('created_at', [$dateFrom, $dateTo])
            ->select('order_type', DB::raw('count(*) as count'), DB::raw('SUM(total_amount) as revenue'))
            ->groupBy('order_type')
            ->get();

        return view('admin.reports.sales', compact(
            'salesData',
            'categoryPerformance',
            'paymentMethods',
            'orderTypes',
            'dateFrom',
            'dateTo',
            'groupBy'
        ));
    }

    /**
     * Customer report.
     */
    public function customers(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        // Customer acquisition
        $newCustomers = User::where('role', 'customer')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->count();

        // Top customers by order count
        $topCustomersByOrders = User::where('role', 'customer')
            ->withCount(['orders' => function($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('created_at', [$dateFrom, $dateTo]);
            }])
            ->having('orders_count', '>', 0)
            ->orderBy('orders_count', 'desc')
            ->limit(10)
            ->get();

        // Top customers by revenue
        $topCustomersByRevenue = User::where('role', 'customer')
            ->withSum(['orders' => function($query) use ($dateFrom, $dateTo) {
                $query->where('payment_status', 'paid')
                      ->whereBetween('created_at', [$dateFrom, $dateTo]);
            }], 'total_amount')
            ->having('orders_sum_total_amount', '>', 0)
            ->orderBy('orders_sum_total_amount', 'desc')
            ->limit(10)
            ->get();

        // Customer registration trends
        $registrationTrends = User::where('role', 'customer')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('admin.reports.customers', compact(
            'newCustomers',
            'topCustomersByOrders',
            'topCustomersByRevenue',
            'registrationTrends',
            'dateFrom',
            'dateTo'
        ));
    }

    /**
     * Inventory report.
     */
    public function inventory()
    {
        // Low stock items
        $lowStockItems = FoodItem::whereNotNull('stock_quantity')
            ->where('stock_quantity', '<=', 10)
            ->where('is_available', true)
            ->orderBy('stock_quantity')
            ->get();

        // Out of stock items
        $outOfStockItems = FoodItem::where('stock_quantity', 0)
            ->orWhere('is_available', false)
            ->get();

        // Most popular items
        $popularItems = FoodItem::withCount(['orderItems'])
            ->orderBy('order_items_count', 'desc')
            ->limit(20)
            ->get();

        // Category distribution
        $categoryDistribution = Category::withCount('foodItems')
            ->orderBy('food_items_count', 'desc')
            ->get();

        return view('admin.reports.inventory', compact(
            'lowStockItems',
            'outOfStockItems',
            'popularItems',
            'categoryDistribution'
        ));
    }

    /**
     * Export sales report to CSV.
     */
    public function exportSales(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $orders = Order::with(['user', 'orderItems.foodItem'])
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->get();

        $filename = 'sales_report_' . $dateFrom . '_to_' . $dateTo . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($orders) {
            $file = fopen('php://output', 'w');
            
            fputcsv($file, [
                'Order Number',
                'Customer',
                'Email',
                'Order Date',
                'Items',
                'Subtotal',
                'Tax',
                'Delivery Fee',
                'Total',
                'Payment Method',
                'Payment Status',
                'Order Status'
            ]);

            foreach ($orders as $order) {
                fputcsv($file, [
                    $order->order_number,
                    $order->customer_name,
                    $order->customer_email,
                    $order->created_at->format('Y-m-d H:i:s'),
                    $order->orderItems->count(),
                    $order->subtotal,
                    $order->tax_amount,
                    $order->delivery_fee,
                    $order->total_amount,
                    $order->payment_method,
                    $order->payment_status,
                    $order->status
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get sales data for charts.
     */
    private function getSalesData($dateFrom, $dateTo, $groupBy)
    {
        $format = match($groupBy) {
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            default => '%Y-%m-%d'
        };

        return Order::where('payment_status', 'paid')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->select(
                DB::raw("DATE_FORMAT(created_at, '$format') as period"),
                DB::raw('SUM(total_amount) as revenue'),
                DB::raw('COUNT(*) as order_count')
            )
            ->groupBy('period')
            ->orderBy('period')
            ->get();
    }

    /**
     * Get category performance data.
     */
    private function getCategoryPerformance($dateFrom, $dateTo)
    {
        return Category::withSum(['foodItems.orderItems' => function($query) use ($dateFrom, $dateTo) {
                $query->whereHas('order', function($q) use ($dateFrom, $dateTo) {
                    $q->where('payment_status', 'paid')
                      ->whereBetween('created_at', [$dateFrom, $dateTo]);
                });
            }], 'total_price')
            ->withCount(['foodItems.orderItems' => function($query) use ($dateFrom, $dateTo) {
                $query->whereHas('order', function($q) use ($dateFrom, $dateTo) {
                    $q->whereBetween('created_at', [$dateFrom, $dateTo]);
                });
            }])
            ->having('food_items_order_items_sum_total_price', '>', 0)
            ->orderBy('food_items_order_items_sum_total_price', 'desc')
            ->get();
    }
}
