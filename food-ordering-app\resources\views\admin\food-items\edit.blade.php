@extends('admin.layouts.app')

@section('title', 'Edit Food Item')
@section('page-title', 'Edit Food Item')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('admin.food-items.index') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Food Items
    </a>
    <a href="{{ route('admin.food-items.show', $foodItem) }}" class="btn btn-outline-info">
        <i class="bi bi-eye me-1"></i>
        View Food Item
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Edit Food Item Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.food-items.update', $foodItem) }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Food Item Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $foodItem->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="image" class="form-label">Food Image</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Leave empty to keep current image. Max size: 2MB</small>
                        </div>
                    </div>
                    
                    @if($foodItem->image)
                        <div class="mb-3">
                            <label class="form-label">Current Image</label>
                            <div>
                                <img src="{{ Storage::url($foodItem->image) }}" alt="{{ $foodItem->name }}" 
                                     class="rounded" style="width: 150px; height: 150px; object-fit: cover;">
                            </div>
                        </div>
                    @endif
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4" required>{{ old('description', $foodItem->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="ingredients" class="form-label">Ingredients</label>
                        <textarea class="form-control @error('ingredients') is-invalid @enderror" 
                                  id="ingredients" name="ingredients" rows="3">{{ old('ingredients', $foodItem->ingredients) }}</textarea>
                        @error('ingredients')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">List main ingredients separated by commas</small>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="price_per_unit" class="form-label">Price per Unit <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control @error('price_per_unit') is-invalid @enderror" 
                                       id="price_per_unit" name="price_per_unit" value="{{ old('price_per_unit', $foodItem->price_per_unit) }}" 
                                       step="0.01" min="0" required>
                                @error('price_per_unit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="price_per_kg" class="form-label">Price per KG</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control @error('price_per_kg') is-invalid @enderror" 
                                       id="price_per_kg" name="price_per_kg" value="{{ old('price_per_kg', $foodItem->price_per_kg) }}" 
                                       step="0.01" min="0">
                                @error('price_per_kg')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="unit" class="form-label">Unit <span class="text-danger">*</span></label>
                            <select class="form-select @error('unit') is-invalid @enderror" id="unit" name="unit" required>
                                <option value="">Select Unit</option>
                                <option value="piece" {{ old('unit', $foodItem->unit) === 'piece' ? 'selected' : '' }}>Piece</option>
                                <option value="plate" {{ old('unit', $foodItem->unit) === 'plate' ? 'selected' : '' }}>Plate</option>
                                <option value="bowl" {{ old('unit', $foodItem->unit) === 'bowl' ? 'selected' : '' }}>Bowl</option>
                                <option value="cup" {{ old('unit', $foodItem->unit) === 'cup' ? 'selected' : '' }}>Cup</option>
                                <option value="kg" {{ old('unit', $foodItem->unit) === 'kg' ? 'selected' : '' }}>Kilogram</option>
                                <option value="gram" {{ old('unit', $foodItem->unit) === 'gram' ? 'selected' : '' }}>Gram</option>
                                <option value="liter" {{ old('unit', $foodItem->unit) === 'liter' ? 'selected' : '' }}>Liter</option>
                                <option value="ml" {{ old('unit', $foodItem->unit) === 'ml' ? 'selected' : '' }}>Milliliter</option>
                            </select>
                            @error('unit')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                            <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ old('category_id', $foodItem->category_id) == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="cuisine_id" class="form-label">Cuisine <span class="text-danger">*</span></label>
                            <select class="form-select @error('cuisine_id') is-invalid @enderror" id="cuisine_id" name="cuisine_id" required>
                                <option value="">Select Cuisine</option>
                                @foreach($cuisines as $cuisine)
                                    <option value="{{ $cuisine->id }}" {{ old('cuisine_id', $foodItem->cuisine_id) == $cuisine->id ? 'selected' : '' }}>
                                        {{ $cuisine->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('cuisine_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="stock_quantity" class="form-label">Stock Quantity</label>
                            <input type="number" class="form-control @error('stock_quantity') is-invalid @enderror" 
                                   id="stock_quantity" name="stock_quantity" value="{{ old('stock_quantity', $foodItem->stock_quantity) }}" min="0">
                            @error('stock_quantity')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Leave empty for unlimited stock</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="spice_level" class="form-label">Spice Level</label>
                            <select class="form-select @error('spice_level') is-invalid @enderror" id="spice_level" name="spice_level">
                                <option value="">Not Applicable</option>
                                <option value="1" {{ old('spice_level', $foodItem->spice_level) == '1' ? 'selected' : '' }}>1 - Mild</option>
                                <option value="2" {{ old('spice_level', $foodItem->spice_level) == '2' ? 'selected' : '' }}>2 - Medium</option>
                                <option value="3" {{ old('spice_level', $foodItem->spice_level) == '3' ? 'selected' : '' }}>3 - Hot</option>
                                <option value="4" {{ old('spice_level', $foodItem->spice_level) == '4' ? 'selected' : '' }}>4 - Very Hot</option>
                                <option value="5" {{ old('spice_level', $foodItem->spice_level) == '5' ? 'selected' : '' }}>5 - Extremely Hot</option>
                            </select>
                            @error('spice_level')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Dietary Information</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_vegetarian" name="is_vegetarian" 
                                       {{ old('is_vegetarian', $foodItem->is_vegetarian) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_vegetarian">Vegetarian</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_vegan" name="is_vegan" 
                                       {{ old('is_vegan', $foodItem->is_vegan) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_vegan">Vegan</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_gluten_free" name="is_gluten_free" 
                                       {{ old('is_gluten_free', $foodItem->is_gluten_free) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_gluten_free">Gluten Free</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_spicy" name="is_spicy" 
                                       {{ old('is_spicy', $foodItem->is_spicy) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_spicy">Spicy</label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>Status & Features</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_available" name="is_available" 
                                       {{ old('is_available', $foodItem->is_available) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_available">Available for Order</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_popular" name="is_popular" 
                                       {{ old('is_popular', $foodItem->is_popular) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_popular">Popular Item</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                       {{ old('is_featured', $foodItem->is_featured) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_featured">Featured Item</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="allow_bulk_order" name="allow_bulk_order" 
                                       {{ old('allow_bulk_order', $foodItem->allow_bulk_order) ? 'checked' : '' }}>
                                <label class="form-check-label" for="allow_bulk_order">Allow Bulk Orders</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>
                            Update Food Item
                        </button>
                        <a href="{{ route('admin.food-items.show', $foodItem) }}" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Current Food Item</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    @if($foodItem->image)
                        <img src="{{ Storage::url($foodItem->image) }}" alt="{{ $foodItem->name }}" 
                             class="rounded" style="width: 120px; height: 120px; object-fit: cover;">
                    @else
                        <div class="bg-light rounded d-inline-flex align-items-center justify-content-center" 
                             style="width: 120px; height: 120px;">
                            <i class="bi bi-image text-muted fs-3"></i>
                        </div>
                    @endif
                    <h6 class="mt-2 mb-0">{{ $foodItem->name }}</h6>
                    <small class="text-muted">${{ number_format($foodItem->price_per_unit, 2) }} per {{ $foodItem->unit }}</small>
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <small class="text-muted">Status</small>
                            <div>
                                <span class="badge bg-{{ $foodItem->is_available ? 'success' : 'secondary' }}">
                                    {{ $foodItem->is_available ? 'Available' : 'Unavailable' }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Category</small>
                        <div>
                            <span class="badge bg-info">
                                {{ $foodItem->category->name ?? 'None' }}
                            </span>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="small">
                    <div class="mb-2">
                        <strong>Created:</strong><br>
                        {{ $foodItem->created_at->format('M d, Y') }}
                    </div>
                    <div class="mb-2">
                        <strong>Last Updated:</strong><br>
                        {{ $foodItem->updated_at->format('M d, Y H:i A') }}
                    </div>
                    <div>
                        <strong>Total Orders:</strong><br>
                        {{ $foodItem->orderItems ? $foodItem->orderItems->count() : 0 }}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Edit Guidelines</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">Important Notes</h6>
                    <ul class="mb-0 small">
                        <li>Changes will be reflected immediately on the website</li>
                        <li>Price changes affect new orders only</li>
                        <li>Image changes may take a few minutes to appear</li>
                        <li>Availability changes affect ordering immediately</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
