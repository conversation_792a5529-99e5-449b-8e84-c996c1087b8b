@extends('admin.layouts.app')

@section('title', 'Create Category')
@section('page-title', 'Create New Category')

@section('page-actions')
<a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-1"></i>
    Back to Categories
</a>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Category Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.categories.store') }}" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">Parent Category</label>
                                <select class="form-select @error('parent_id') is-invalid @enderror"
                                        id="parent_id" name="parent_id">
                                    <option value="">Main Category (No Parent)</option>
                                    @foreach($mainCategories as $category)
                                        <option value="{{ $category->id }}" {{ old('parent_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('parent_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Leave empty to create a main category</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Lower numbers appear first</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3" id="category-type-info">
                                <label class="form-label">Category Type</label>
                                <div class="form-control-plaintext">
                                    <span id="category-type-text" class="badge bg-primary">Main Category</span>
                                </div>
                                <div class="form-text">This will be determined by parent selection</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">Category Image</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                   value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                        <div class="form-text">Inactive categories won't be visible to customers</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>
                            Create Category
                        </button>
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Category Guidelines</h6>
            </div>
            <div class="card-body">
                <div class="small text-muted">
                    <h6>Naming Guidelines:</h6>
                    <ul class="mb-3">
                        <li>Use clear, descriptive names</li>
                        <li>Keep names concise (under 50 characters)</li>
                        <li>Avoid special characters</li>
                        <li>Use title case (e.g., "Main Dishes")</li>
                    </ul>
                    
                    <h6>Image Guidelines:</h6>
                    <ul class="mb-3">
                        <li>Recommended size: 400x300 pixels</li>
                        <li>Use high-quality images</li>
                        <li>Ensure good lighting and contrast</li>
                        <li>Avoid text overlays</li>
                    </ul>
                    
                    <h6>Organization Tips:</h6>
                    <ul class="mb-0">
                        <li>Use sort order to control display sequence</li>
                        <li>Group related items logically</li>
                        <li>Consider customer browsing patterns</li>
                        <li>Review and update regularly</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Preview</h6>
            </div>
            <div class="card-body">
                <div id="categoryPreview" class="text-center">
                    <div class="bg-light rounded mb-2" style="height: 120px; display: flex; align-items: center; justify-content: center;">
                        <i class="bi bi-image text-muted fs-1"></i>
                    </div>
                    <div class="fw-bold" id="previewName">Category Name</div>
                    <div class="text-muted small" id="previewDescription">Category description will appear here</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Live preview functionality
document.getElementById('name').addEventListener('input', function() {
    document.getElementById('previewName').textContent = this.value || 'Category Name';
});

document.getElementById('description').addEventListener('input', function() {
    document.getElementById('previewDescription').textContent = this.value || 'Category description will appear here';
});

// Parent category selection handler
document.getElementById('parent_id').addEventListener('change', function() {
    const categoryTypeText = document.getElementById('category-type-text');
    const categoryTypeBadge = categoryTypeText.parentElement;

    if (this.value) {
        categoryTypeText.textContent = 'Subcategory';
        categoryTypeText.className = 'badge bg-success';

        // Update preview to show hierarchy
        const selectedOption = this.options[this.selectedIndex];
        const parentName = selectedOption.text;
        const currentName = document.getElementById('name').value || 'Category Name';
        document.getElementById('previewName').innerHTML = `
            <small class="text-muted">${parentName} →</small><br>
            ${currentName}
        `;
    } else {
        categoryTypeText.textContent = 'Main Category';
        categoryTypeText.className = 'badge bg-primary';

        // Reset preview
        const currentName = document.getElementById('name').value || 'Category Name';
        document.getElementById('previewName').textContent = currentName;
    }
});

// Image preview
document.getElementById('image').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.querySelector('#categoryPreview .bg-light');
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" style="width: 100%; height: 120px; object-fit: cover;" class="rounded">`;
        };
        reader.readAsDataURL(file);
    }
});

// Update preview when name changes and parent is selected
document.getElementById('name').addEventListener('input', function() {
    const parentSelect = document.getElementById('parent_id');
    if (parentSelect.value) {
        const selectedOption = parentSelect.options[parentSelect.selectedIndex];
        const parentName = selectedOption.text;
        const currentName = this.value || 'Category Name';
        document.getElementById('previewName').innerHTML = `
            <small class="text-muted">${parentName} →</small><br>
            ${currentName}
        `;
    } else {
        document.getElementById('previewName').textContent = this.value || 'Category Name';
    }
});
</script>
@endpush
