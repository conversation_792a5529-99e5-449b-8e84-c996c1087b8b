@extends('admin.layouts.app')

@section('title', 'Order Details')
@section('page-title', 'Order Details')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('admin.orders.index') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Orders
    </a>
    <a href="{{ route('admin.orders.invoice', $order) }}" class="btn btn-outline-primary" target="_blank">
        <i class="bi bi-printer me-1"></i>
        Print Invoice
    </a>
    @if(!in_array($order->status, ['delivered', 'cancelled']))
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#statusModal">
            <i class="bi bi-pencil me-1"></i>
            Update Status
        </button>
    @endif
    @if($order->payment_status !== 'paid')
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#paymentModal">
            <i class="bi bi-credit-card me-1"></i>
            Update Payment
        </button>
    @endif
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8">
        <!-- Order Information -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Order #{{ $order->order_number }}</h5>
                    <div>
                        <span class="badge bg-{{ $order->status === 'delivered' ? 'success' : ($order->status === 'cancelled' ? 'danger' : 'warning') }} fs-6">
                            {{ ucfirst(str_replace('_', ' ', $order->status)) }}
                        </span>
                        <span class="badge bg-{{ $order->payment_status === 'paid' ? 'success' : ($order->payment_status === 'failed' ? 'danger' : 'warning') }} fs-6">
                            {{ ucfirst($order->payment_status) }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Customer Information</h6>
                        <div class="mb-2">
                            <strong>Name:</strong> {{ $order->customer_name }}
                        </div>
                        <div class="mb-2">
                            <strong>Email:</strong> {{ $order->customer_email }}
                        </div>
                        @if($order->customer_phone)
                            <div class="mb-2">
                                <strong>Phone:</strong> {{ $order->customer_phone }}
                            </div>
                        @endif
                        @if($order->user)
                            <div class="mb-2">
                                <strong>Account:</strong> 
                                <a href="{{ route('admin.users.show', $order->user) }}" class="text-decoration-none">
                                    Registered User
                                </a>
                            </div>
                        @endif
                    </div>
                    
                    <div class="col-md-6">
                        <h6>Order Details</h6>
                        <div class="mb-2">
                            <strong>Order Type:</strong> {{ ucfirst(str_replace('_', ' ', $order->order_type)) }}
                        </div>
                        <div class="mb-2">
                            <strong>Payment Method:</strong> {{ ucfirst($order->payment_method) }}
                        </div>
                        <div class="mb-2">
                            <strong>Order Date:</strong> {{ $order->created_at->format('M d, Y H:i A') }}
                        </div>
                        @if($order->estimated_delivery_time)
                            <div class="mb-2">
                                <strong>Estimated Delivery:</strong> {{ $order->estimated_delivery_time->format('M d, Y H:i A') }}
                            </div>
                        @endif
                    </div>
                </div>
                
                @if($order->order_type === 'delivery')
                    <hr>
                    <h6>Delivery Address</h6>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-2">
                                <strong>Address:</strong> {{ $order->delivery_address }}
                            </div>
                            <div class="mb-2">
                                <strong>City:</strong> {{ $order->delivery_city }}
                            </div>
                            <div class="mb-2">
                                <strong>Postal Code:</strong> {{ $order->delivery_postal_code }}
                            </div>
                            @if($order->delivery_instructions)
                                <div class="mb-2">
                                    <strong>Instructions:</strong> {{ $order->delivery_instructions }}
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
                
                @if($order->notes)
                    <hr>
                    <h6>Order Notes</h6>
                    <p class="text-muted">{{ $order->notes }}</p>
                @endif
            </div>
        </div>
        
        <!-- Order Items -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Order Items</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Item</th>
                                <th>Unit Price</th>
                                <th>Quantity</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($order->orderItems as $item)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($item->foodItem && $item->foodItem->image)
                                                <img src="{{ Storage::url($item->foodItem->image) }}" alt="{{ $item->item_name }}" 
                                                     class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="bi bi-image text-muted"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="fw-bold">{{ $item->item_name }}</div>
                                                @if($item->foodItem)
                                                    <small class="text-muted">
                                                        <a href="{{ route('admin.food-items.show', $item->foodItem) }}" class="text-decoration-none">
                                                            View Item Details
                                                        </a>
                                                    </small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>${{ number_format($item->unit_price, 2) }}</td>
                                    <td>{{ $item->quantity }} {{ $item->unit }}</td>
                                    <td class="fw-bold">${{ number_format($item->total_price, 2) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Order Timeline -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Order Timeline</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Order Placed</h6>
                            <p class="text-muted mb-0">{{ $order->created_at->format('M d, Y H:i A') }}</p>
                        </div>
                    </div>
                    
                    @if($order->status !== 'pending')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Status: {{ ucfirst(str_replace('_', ' ', $order->status)) }}</h6>
                                <p class="text-muted mb-0">{{ $order->updated_at->format('M d, Y H:i A') }}</p>
                            </div>
                        </div>
                    @endif
                    
                    @if($order->payment_status === 'paid')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Payment Confirmed</h6>
                                <p class="text-muted mb-0">Payment via {{ ucfirst($order->payment_method) }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Order Summary -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Order Summary</h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>Subtotal:</span>
                    <span>${{ number_format($order->subtotal, 2) }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Tax:</span>
                    <span>${{ number_format($order->tax_amount, 2) }}</span>
                </div>
                @if($order->delivery_fee > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>Delivery Fee:</span>
                        <span>${{ number_format($order->delivery_fee, 2) }}</span>
                    </div>
                @endif
                @if($order->discount_amount > 0)
                    <div class="d-flex justify-content-between mb-2 text-success">
                        <span>Discount:</span>
                        <span>-${{ number_format($order->discount_amount, 2) }}</span>
                    </div>
                @endif
                <hr>
                <div class="d-flex justify-content-between fw-bold">
                    <span>Total:</span>
                    <span class="text-primary">${{ number_format($order->total_amount, 2) }}</span>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.orders.invoice', $order) }}" class="btn btn-outline-primary" target="_blank">
                        <i class="bi bi-printer me-1"></i>
                        Print Invoice
                    </a>
                    
                    @if(!in_array($order->status, ['delivered', 'cancelled']))
                        <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#statusModal">
                            <i class="bi bi-pencil me-1"></i>
                            Update Status
                        </button>
                    @endif
                    
                    @if($order->payment_status !== 'paid')
                        <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#paymentModal">
                            <i class="bi bi-credit-card me-1"></i>
                            Update Payment
                        </button>
                    @endif
                    
                    @if($order->user)
                        <a href="{{ route('admin.users.show', $order->user) }}" class="btn btn-outline-info">
                            <i class="bi bi-person me-1"></i>
                            View Customer
                        </a>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Order Statistics -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Order Info</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <strong>Order ID:</strong><br>
                        {{ $order->id }}
                    </div>
                    <div class="mb-2">
                        <strong>Items Count:</strong><br>
                        {{ $order->orderItems->count() }} items
                    </div>
                    <div class="mb-2">
                        <strong>Total Quantity:</strong><br>
                        {{ $order->orderItems->sum('quantity') }} units
                    </div>
                    <div>
                        <strong>Last Updated:</strong><br>
                        {{ $order->updated_at->format('M d, Y H:i A') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.orders.update-status', $order) }}">
                @csrf
                @method('PATCH')
                <div class="modal-header">
                    <h5 class="modal-title">Update Order Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Current Status</label>
                        <input type="text" class="form-control" value="{{ ucfirst(str_replace('_', ' ', $order->status)) }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">New Status</label>
                        <select name="status" class="form-select" required>
                            <option value="pending" {{ $order->status === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="confirmed" {{ $order->status === 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                            <option value="preparing" {{ $order->status === 'preparing' ? 'selected' : '' }}>Preparing</option>
                            <option value="ready" {{ $order->status === 'ready' ? 'selected' : '' }}>Ready</option>
                            <option value="out_for_delivery" {{ $order->status === 'out_for_delivery' ? 'selected' : '' }}>Out for Delivery</option>
                            <option value="delivered" {{ $order->status === 'delivered' ? 'selected' : '' }}>Delivered</option>
                            <option value="cancelled" {{ $order->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes (Optional)</label>
                        <textarea name="notes" class="form-control" rows="3" placeholder="Add any notes about this status update..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Payment Status Update Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.orders.update-payment-status', $order) }}">
                @csrf
                @method('PATCH')
                <div class="modal-header">
                    <h5 class="modal-title">Update Payment Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Current Payment Status</label>
                        <input type="text" class="form-control" value="{{ ucfirst($order->payment_status) }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">New Payment Status</label>
                        <select name="payment_status" class="form-select" required>
                            <option value="pending" {{ $order->payment_status === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="paid" {{ $order->payment_status === 'paid' ? 'selected' : '' }}>Paid</option>
                            <option value="failed" {{ $order->payment_status === 'failed' ? 'selected' : '' }}>Failed</option>
                            <option value="refunded" {{ $order->payment_status === 'refunded' ? 'selected' : '' }}>Refunded</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Update Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>
@endpush
