<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, migrate existing food_type data from is_vegetarian
        if (Schema::hasColumn('food_items', 'is_vegetarian')) {
            DB::statement("UPDATE food_items SET food_type = CASE WHEN is_vegetarian = 1 THEN 'veg' ELSE 'non_veg' END");
        }

        // Migrate existing category relationships to many-to-many
        if (Schema::hasColumn('food_items', 'category_id')) {
            // Insert existing category relationships into the pivot table
            DB::statement("
                INSERT INTO food_item_categories (food_item_id, category_id, is_primary, sort_order, created_at, updated_at)
                SELECT id, category_id, 1, 0, NOW(), NOW()
                FROM food_items 
                WHERE category_id IS NOT NULL
            ");
        }

        // Migrate existing cuisine relationships to many-to-many
        if (Schema::hasColumn('food_items', 'cuisine_id')) {
            // Insert existing cuisine relationships into the pivot table
            DB::statement("
                INSERT INTO food_item_cuisines (food_item_id, cuisine_id, is_primary, sort_order, created_at, updated_at)
                SELECT id, cuisine_id, 1, 0, NOW(), NOW()
                FROM food_items 
                WHERE cuisine_id IS NOT NULL
            ");
        }

        // Remove old columns after migration
        Schema::table('food_items', function (Blueprint $table) {
            if (Schema::hasColumn('food_items', 'is_vegetarian')) {
                $table->dropColumn('is_vegetarian');
            }
            if (Schema::hasColumn('food_items', 'category_id')) {
                $table->dropForeign(['category_id']);
                $table->dropColumn('category_id');
            }
            if (Schema::hasColumn('food_items', 'cuisine_id')) {
                $table->dropForeign(['cuisine_id']);
                $table->dropColumn('cuisine_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back the old columns
        Schema::table('food_items', function (Blueprint $table) {
            $table->boolean('is_vegetarian')->default(false);
            $table->foreignId('category_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('cuisine_id')->nullable()->constrained()->onDelete('cascade');
        });

        // Migrate data back from many-to-many to single relationships
        DB::statement("
            UPDATE food_items 
            SET is_vegetarian = CASE WHEN food_type = 'veg' THEN 1 ELSE 0 END
        ");

        // Migrate primary category back
        DB::statement("
            UPDATE food_items 
            SET category_id = (
                SELECT category_id 
                FROM food_item_categories 
                WHERE food_item_categories.food_item_id = food_items.id 
                AND is_primary = 1 
                LIMIT 1
            )
        ");

        // Migrate primary cuisine back
        DB::statement("
            UPDATE food_items 
            SET cuisine_id = (
                SELECT cuisine_id 
                FROM food_item_cuisines 
                WHERE food_item_cuisines.food_item_id = food_items.id 
                AND is_primary = 1 
                LIMIT 1
            )
        ");

        // Remove the food_type column
        Schema::table('food_items', function (Blueprint $table) {
            $table->dropColumn('food_type');
        });
    }
};
