<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use Illuminate\Support\Str;

class HierarchicalCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create main categories with their subcategories
        $categoriesData = [
            'Appetizers' => [
                'Hot Appetizers',
                'Cold Appetizers',
                'Finger Foods',
                'Dips & Spreads'
            ],
            'Main Courses' => [
                'Vegetarian',
                'Non-Vegetarian',
                'Seafood',
                'Grilled Items',
                'Pasta & Noodles'
            ],
            'Desserts' => [
                'Cakes',
                'Ice Cream',
                'Traditional Sweets',
                'Pastries',
                'Fruit Desserts'
            ],
            'Beverages' => [
                'Hot Beverages',
                'Cold Beverages',
                'Fresh Juices',
                'Smoothies',
                'Alcoholic Drinks'
            ],
            'Salads' => [
                'Green Salads',
                'Fruit Salads',
                'Protein Salads',
                'Grain Salads'
            ],
            'Soups' => [
                'Hot Soups',
                'Cold Soups',
                'Broths',
                'Cream Soups'
            ]
        ];

        foreach ($categoriesData as $mainCategoryName => $subcategories) {
            // Create main category
            $mainCategory = Category::firstOrCreate(
                ['name' => $mainCategoryName],
                [
                    'slug' => Str::slug($mainCategoryName),
                    'description' => "Delicious {$mainCategoryName} for all occasions",
                    'is_active' => true,
                    'sort_order' => array_search($mainCategoryName, array_keys($categoriesData)) * 10,
                    'parent_id' => null
                ]
            );

            // Create subcategories
            foreach ($subcategories as $index => $subcategoryName) {
                Category::firstOrCreate(
                    ['name' => $subcategoryName, 'parent_id' => $mainCategory->id],
                    [
                        'slug' => Str::slug($subcategoryName),
                        'description' => "Fresh and tasty {$subcategoryName}",
                        'is_active' => true,
                        'sort_order' => ($index + 1) * 10,
                        'parent_id' => $mainCategory->id
                    ]
                );
            }
        }

        $this->command->info('Hierarchical categories created successfully!');
    }
}
