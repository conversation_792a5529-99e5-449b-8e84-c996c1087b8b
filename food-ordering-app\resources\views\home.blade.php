@extends('layouts.app')

@section('title', 'Home - Food Ordering App')

@section('content')
<div class="container-mobile">
    <!-- Hero Section -->
    <section class="py-8">
        <div class="bg-gradient-to-r from-orange-500 to-red-500 rounded-lg p-6 text-white">
            <h1 class="text-2xl md:text-3xl font-bold mb-2">Delicious Food Delivered</h1>
            <p class="text-orange-100 mb-4">Order your favorite meals from the comfort of your home</p>
            <a href="{{ route('menu.index') }}" class="bg-white text-orange-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Order Now
            </a>
        </div>
    </section>

    <!-- Catering CTA -->
    <section class="mb-8">
        <div class="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-concierge-bell text-2xl mr-3"></i>
                        <h2 class="text-xl font-bold">Planning an Event?</h2>
                    </div>
                    <p class="text-purple-100 mb-4">Professional catering services for corporate events, weddings, parties, and more!</p>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <a href="{{ route('catering.index') }}" class="bg-white text-purple-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors text-center">
                            View Catering Options
                        </a>
                        <a href="{{ route('catering.booking') }}" class="bg-purple-500 text-white px-6 py-2 rounded-lg font-semibold hover:bg-purple-400 transition-colors text-center border border-purple-400">
                            Get Quote
                        </a>
                    </div>
                </div>
                <div class="hidden md:block ml-6">
                    <i class="fas fa-users text-6xl text-purple-200"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Filters -->
    <section class="mb-8">
        <div class="flex space-x-2 overflow-x-auto pb-2">
            <a href="{{ route('search', ['vegetarian' => 1]) }}" class="filter-btn bg-green-100 text-green-800 px-4 py-2 rounded-full whitespace-nowrap">
                <i class="fas fa-leaf mr-1"></i> Vegetarian
            </a>
            <a href="{{ route('search', ['popular' => 1]) }}" class="filter-btn bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full whitespace-nowrap">
                <i class="fas fa-star mr-1"></i> Popular
            </a>
            <a href="{{ route('search', ['order_type' => 'bulk']) }}" class="filter-btn bg-blue-100 text-blue-800 px-4 py-2 rounded-full whitespace-nowrap">
                <i class="fas fa-weight mr-1"></i> Bulk Order
            </a>
            <a href="{{ route('catering.index') }}" class="filter-btn bg-purple-100 text-purple-800 px-4 py-2 rounded-full whitespace-nowrap">
                <i class="fas fa-concierge-bell mr-1"></i> Catering
            </a>
        </div>
    </section>

    <!-- Categories -->
    <section class="mb-8">
        <h2 class="text-xl font-bold mb-4">Browse by Category</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            @foreach($categories as $category)
            <div class="relative group">
                <a href="{{ route('search', ['category' => $category->id]) }}" class="bg-white rounded-lg p-4 text-center shadow-sm hover:shadow-md transition-shadow block">
                    @if($category->image)
                        <img src="{{ $category->image }}" alt="{{ $category->name }}" class="w-12 h-12 mx-auto mb-2 rounded-full object-cover">
                    @else
                        <div class="w-12 h-12 mx-auto mb-2 bg-orange-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-utensils text-orange-600"></i>
                        </div>
                    @endif
                    <h3 class="font-semibold text-sm">{{ $category->name }}</h3>
                    @if($category->subcategories->count() > 0)
                        <span class="text-xs text-gray-500 mt-1 block">{{ $category->subcategories->count() }} subcategories</span>
                    @endif
                </a>

                <!-- Subcategories Dropdown -->
                @if($category->subcategories->count() > 0)
                <div class="absolute top-full left-0 right-0 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10 mt-1">
                    <div class="p-2">
                        <div class="text-xs font-semibold text-gray-700 mb-2 px-2">{{ $category->name }} Subcategories:</div>
                        @foreach($category->subcategories->take(5) as $subcategory)
                            <a href="{{ route('search', ['category' => $category->id, 'subcategory' => $subcategory->id]) }}"
                               class="block px-2 py-1 text-xs text-gray-600 hover:bg-orange-50 hover:text-orange-600 rounded transition-colors">
                                {{ $subcategory->name }}
                            </a>
                        @endforeach
                        @if($category->subcategories->count() > 5)
                            <a href="{{ route('search', ['category' => $category->id]) }}"
                               class="block px-2 py-1 text-xs text-orange-600 font-medium hover:bg-orange-50 rounded transition-colors">
                                View all {{ $category->subcategories->count() }} subcategories →
                            </a>
                        @endif
                    </div>
                </div>
                @endif
            </div>
            @endforeach
        </div>
    </section>

    <!-- Cuisines -->
    <section class="mb-8">
        <h2 class="text-xl font-bold mb-4">Explore Cuisines</h2>
        <div class="flex space-x-4 overflow-x-auto pb-2">
            @foreach($cuisines as $cuisine)
            <a href="{{ route('search', ['cuisine' => $cuisine->id]) }}" class="bg-white rounded-lg p-4 text-center shadow-sm hover:shadow-md transition-shadow min-w-[120px]">
                @if($cuisine->image)
                    <img src="{{ $cuisine->image }}" alt="{{ $cuisine->name }}" class="w-16 h-16 mx-auto mb-2 rounded-full object-cover">
                @else
                    <div class="w-16 h-16 mx-auto mb-2 bg-orange-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-globe text-orange-600"></i>
                    </div>
                @endif
                <h3 class="font-semibold text-sm">{{ $cuisine->name }}</h3>
            </a>
            @endforeach
        </div>
    </section>

    <!-- Featured Packages -->
    @if($featuredPackages->count() > 0)
    <section class="mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">Featured Packages</h2>
            <a href="{{ route('menu.index') }}" class="text-orange-600 text-sm font-semibold">View All</a>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            @foreach($featuredPackages as $package)
            <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="relative">
                    @if($package->image)
                        <img src="{{ $package->image }}" alt="{{ $package->name }}" class="w-full h-32 object-cover">
                    @else
                        <div class="w-full h-32 bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-image text-gray-400 text-2xl"></i>
                        </div>
                    @endif
                    @if($package->is_popular)
                        <span class="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-semibold">
                            <i class="fas fa-star mr-1"></i>Popular
                        </span>
                    @endif
                    @if($package->discount_percentage > 0)
                        <span class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                            {{ $package->discount_percentage }}% OFF
                        </span>
                    @endif
                </div>
                <div class="p-4">
                    <h3 class="font-semibold mb-1">{{ $package->name }}</h3>
                    <p class="text-gray-600 text-sm mb-2 line-clamp-2">{{ $package->description }}</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-orange-600">${{ number_format($package->price, 2) }}</span>
                            @if($package->original_price && $package->original_price > $package->price)
                                <span class="text-sm text-gray-500 line-through">${{ number_format($package->original_price, 2) }}</span>
                            @endif
                        </div>
                        <button onclick="addToCart('package', {{ $package->id }})" class="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700 transition-colors">
                            Add to Cart
                        </button>
                    </div>
                    @if($package->serves_people)
                        <p class="text-xs text-gray-500 mt-1">Serves {{ $package->serves_people }} people</p>
                    @endif
                </div>
            </div>
            @endforeach
        </div>
    </section>
    @endif

    <!-- Popular Food Items -->
    @if($popularFoodItems->count() > 0)
    <section class="mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">Popular Items</h2>
            <a href="{{ route('search', ['popular' => 1]) }}" class="text-orange-600 text-sm font-semibold">View All</a>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            @foreach($popularFoodItems as $item)
            <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="relative">
                    @if($item->image)
                        <img src="{{ $item->image }}" alt="{{ $item->name }}" class="w-full h-24 object-cover">
                    @else
                        <div class="w-full h-24 bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-image text-gray-400"></i>
                        </div>
                    @endif
                    @if($item->is_vegetarian)
                        <span class="absolute top-1 left-1 bg-green-500 text-white px-1 py-0.5 rounded text-xs">
                            <i class="fas fa-leaf"></i>
                        </span>
                    @endif
                    @if($item->is_spicy)
                        <span class="absolute top-1 right-1 bg-red-500 text-white px-1 py-0.5 rounded text-xs">
                            <i class="fas fa-pepper-hot"></i>
                        </span>
                    @endif
                </div>
                <div class="p-3">
                    <h3 class="font-semibold text-sm mb-1">{{ $item->name }}</h3>
                    <p class="text-xs text-gray-600 mb-2">{{ $item->category->name }}</p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-bold text-orange-600">${{ number_format($item->price_per_unit, 2) }}</span>
                        <button onclick="addToCart('food_item', {{ $item->id }})" class="bg-orange-600 text-white px-2 py-1 rounded text-xs hover:bg-orange-700 transition-colors">
                            Add
                        </button>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </section>
    @endif

    <!-- Featured Food Items -->
    @if($featuredFoodItems->count() > 0)
    <section class="mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">Featured Items</h2>
            <a href="{{ route('menu.index') }}" class="text-orange-600 text-sm font-semibold">View All</a>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            @foreach($featuredFoodItems as $item)
            <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="relative">
                    @if($item->image)
                        <img src="{{ $item->image }}" alt="{{ $item->name }}" class="w-full h-24 object-cover">
                    @else
                        <div class="w-full h-24 bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-image text-gray-400"></i>
                        </div>
                    @endif
                    @if($item->is_vegetarian)
                        <span class="absolute top-1 left-1 bg-green-500 text-white px-1 py-0.5 rounded text-xs">
                            <i class="fas fa-leaf"></i>
                        </span>
                    @endif
                    @if($item->is_spicy)
                        <span class="absolute top-1 right-1 bg-red-500 text-white px-1 py-0.5 rounded text-xs">
                            <i class="fas fa-pepper-hot"></i>
                        </span>
                    @endif
                </div>
                <div class="p-3">
                    <h3 class="font-semibold text-sm mb-1">{{ $item->name }}</h3>
                    <p class="text-xs text-gray-600 mb-2">{{ $item->category->name }}</p>
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-sm font-bold text-orange-600">${{ number_format($item->price_per_unit, 2) }}</span>
                            @if($item->allow_bulk_order && $item->price_per_kg)
                                <span class="text-xs text-gray-500 block">${{ number_format($item->price_per_kg, 2) }}/kg</span>
                            @endif
                        </div>
                        <button onclick="addToCart('food_item', {{ $item->id }})" class="bg-orange-600 text-white px-2 py-1 rounded text-xs hover:bg-orange-700 transition-colors">
                            Add
                        </button>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </section>
    @endif
</div>
@endsection
