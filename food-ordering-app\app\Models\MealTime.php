<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class MealTime extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'start_time',
        'end_time',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
    ];

    /**
     * Get the food items for this meal time.
     */
    public function foodItems(): BelongsToMany
    {
        return $this->belongsToMany(FoodItem::class, 'food_item_meal_times')
                    ->withPivot('is_primary', 'sort_order')
                    ->withTimestamps()
                    ->orderByPivot('sort_order')
                    ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get active food items for this meal time.
     */
    public function activeFoodItems(): BelongsToMany
    {
        return $this->foodItems()->where('food_items.is_available', true);
    }

    /**
     * Get primary food items for this meal time.
     */
    public function primaryFoodItems(): BelongsToMany
    {
        return $this->foodItems()->wherePivot('is_primary', true);
    }

    /**
     * Scope to get only active meal times.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Check if this meal time is currently active based on time.
     */
    public function isCurrentlyActive(): bool
    {
        if (!$this->start_time || !$this->end_time) {
            return true; // Always active if no time restrictions
        }

        $now = now()->format('H:i');
        $start = $this->start_time->format('H:i');
        $end = $this->end_time->format('H:i');

        // Handle overnight meal times (e.g., late night snacks)
        if ($start > $end) {
            return $now >= $start || $now <= $end;
        }

        return $now >= $start && $now <= $end;
    }
}
