<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Cuisine;
use App\Models\FoodItem;
use App\Models\Package;
use Illuminate\Http\Request;

class MenuController extends Controller
{
    /**
     * Display the menu page.
     */
    public function index(Request $request)
    {
        $categoryId = $request->get('category');
        $subcategoryId = $request->get('subcategory');
        $cuisineId = $request->get('cuisine');
        $isVegetarian = $request->get('vegetarian');
        $isPopular = $request->get('popular');
        $orderType = $request->get('order_type', 'all'); // all, unit, bulk
        $sortBy = $request->get('sort', 'name'); // name, price, popular

        // Get food items
        $foodItemsQuery = FoodItem::available()->with(['category', 'cuisine']);

        if ($categoryId) {
            $foodItemsQuery->byCategory($categoryId);
        }

        if ($subcategoryId) {
            $foodItemsQuery->byCategory($subcategoryId);
        }

        if ($cuisineId) {
            $foodItemsQuery->byCuisine($cuisineId);
        }

        if ($isVegetarian) {
            $foodItemsQuery->vegetarian();
        }

        if ($isPopular) {
            $foodItemsQuery->where('is_popular', true);
        }

        if ($orderType === 'bulk') {
            $foodItemsQuery->where('allow_bulk_order', true);
        }

        // Apply sorting
        switch ($sortBy) {
            case 'price':
                $foodItemsQuery->orderBy('price_per_unit');
                break;
            case 'popular':
                $foodItemsQuery->orderBy('is_popular', 'desc')->orderBy('name');
                break;
            default:
                $foodItemsQuery->orderBy('sort_order')->orderBy('name');
                break;
        }

        $foodItems = $foodItemsQuery->paginate(16, ['*'], 'food_page');

        // Get packages
        $packagesQuery = Package::available()->with(['category', 'cuisine', 'foodItems']);
        
        if ($categoryId) {
            $packagesQuery->byCategory($categoryId);
        }
        
        if ($cuisineId) {
            $packagesQuery->byCuisine($cuisineId);
        }
        
        if ($isVegetarian) {
            $packagesQuery->vegetarian();
        }

        // Apply sorting for packages
        switch ($sortBy) {
            case 'price':
                $packagesQuery->orderBy('price');
                break;
            case 'popular':
                $packagesQuery->orderBy('is_popular', 'desc')->orderBy('name');
                break;
            default:
                $packagesQuery->orderBy('sort_order')->orderBy('name');
                break;
        }

        $packages = $packagesQuery->paginate(12, ['*'], 'package_page');

        // Get filter options
        $categories = Category::active()->mainCategories()->ordered()->get();
        $cuisines = Cuisine::active()->ordered()->get();

        // Get subcategories for the selected main category
        $subcategories = collect();
        if ($categoryId) {
            $selectedCategory = Category::find($categoryId);
            if ($selectedCategory && $selectedCategory->isMainCategory()) {
                $subcategories = $selectedCategory->subcategories;
            }
        }

        return view('menu.index', compact(
            'foodItems',
            'packages',
            'categories',
            'subcategories',
            'cuisines',
            'categoryId',
            'subcategoryId',
            'cuisineId',
            'isVegetarian',
            'isPopular',
            'orderType',
            'sortBy'
        ));
    }

    /**
     * Display a specific food item.
     */
    public function showFoodItem(FoodItem $foodItem)
    {
        $foodItem->load(['category', 'cuisine']);
        
        // Get related items from the same category
        $relatedItems = FoodItem::available()
            ->byCategory($foodItem->category_id)
            ->where('id', '!=', $foodItem->id)
            ->limit(4)
            ->get();

        return view('menu.food-item', compact('foodItem', 'relatedItems'));
    }

    /**
     * Display a specific package.
     */
    public function showPackage(Package $package)
    {
        $package->load(['category', 'cuisine', 'foodItems']);
        
        // Get related packages from the same category
        $relatedPackages = Package::available()
            ->byCategory($package->category_id)
            ->where('id', '!=', $package->id)
            ->limit(4)
            ->get();

        return view('menu.package', compact('package', 'relatedPackages'));
    }

    /**
     * Get menu items by category (AJAX).
     */
    public function getByCategory(Category $category)
    {
        $foodItems = $category->activeFoodItems()
            ->with(['cuisine'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        $packages = $category->activePackages()
            ->with(['cuisine'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        return response()->json([
            'food_items' => $foodItems,
            'packages' => $packages,
        ]);
    }

    /**
     * Get menu items by cuisine (AJAX).
     */
    public function getByCuisine(Cuisine $cuisine)
    {
        $foodItems = $cuisine->activeFoodItems()
            ->with(['category'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        $packages = $cuisine->activePackages()
            ->with(['category'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        return response()->json([
            'food_items' => $foodItems,
            'packages' => $packages,
        ]);
    }

    /**
     * Get subcategories for a given category (API endpoint).
     */
    public function getSubcategories(Category $category)
    {
        if (!$category->isMainCategory()) {
            return response()->json(['error' => 'Only main categories have subcategories'], 400);
        }

        $subcategories = $category->subcategories()->select('id', 'name', 'slug')->get();

        return response()->json([
            'subcategories' => $subcategories,
            'parent_category' => [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug
            ]
        ]);
    }
}
