@extends('admin.layouts.app')

@section('title', 'Catering Orders Management')
@section('page-title', 'Catering Orders Management')

@section('page-actions')
<div class="btn-group" role="group">
    <a href="{{ route('admin.catering-orders.export') }}{{ request()->getQueryString() ? '?' . request()->getQueryString() : '' }}" 
       class="btn btn-outline-success">
        <i class="bi bi-download me-1"></i>
        Export
    </a>
    <a href="{{ route('admin.catering-orders.statistics') }}" class="btn btn-outline-info">
        <i class="bi bi-bar-chart me-1"></i>
        Statistics
    </a>
</div>
@endsection

@section('content')
<!-- Status Filter Badges -->
<div class="row mb-3">
    <div class="col">
        <div class="d-flex gap-2 flex-wrap">
            <a href="{{ route('admin.catering-orders.index') }}" 
               class="btn btn-outline-primary {{ !request('status') ? 'active' : '' }}">
                All Orders
                <span class="badge bg-primary ms-1">{{ array_sum($statusCounts->toArray()) }}</span>
            </a>
            @foreach(['pending', 'confirmed', 'in_preparation', 'ready', 'in_transit', 'delivered', 'completed', 'cancelled'] as $status)
                <a href="{{ route('admin.catering-orders.index', ['status' => $status]) }}" 
                   class="btn btn-outline-{{ $status === 'completed' || $status === 'delivered' ? 'success' : ($status === 'cancelled' ? 'danger' : 'warning') }} {{ request('status') === $status ? 'active' : '' }}">
                    {{ ucfirst(str_replace('_', ' ', $status)) }}
                    <span class="badge bg-{{ $status === 'completed' || $status === 'delivered' ? 'success' : ($status === 'cancelled' ? 'danger' : 'warning') }} ms-1">
                        {{ $statusCounts[$status] ?? 0 }}
                    </span>
                </a>
            @endforeach
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">All Catering Orders</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="{{ route('admin.catering-orders.index') }}" class="d-flex gap-2">
                    <input type="text" name="search" class="form-control form-control-sm" 
                           placeholder="Search orders..." value="{{ request('search') }}">
                    
                    <select name="event_type_id" class="form-select form-select-sm">
                        <option value="">All Event Types</option>
                        @foreach($eventTypes as $eventType)
                            <option value="{{ $eventType->id }}" {{ request('event_type_id') == $eventType->id ? 'selected' : '' }}>
                                {{ $eventType->name }}
                            </option>
                        @endforeach
                    </select>
                    
                    <input type="date" name="date_from" class="form-control form-control-sm" 
                           value="{{ request('date_from') }}" placeholder="From Date">
                    
                    <input type="date" name="date_to" class="form-control form-control-sm" 
                           value="{{ request('date_to') }}" placeholder="To Date">
                    
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-search"></i>
                    </button>
                    
                    @if(request()->hasAny(['search', 'event_type_id', 'date_from', 'date_to']))
                        <a href="{{ route('admin.catering-orders.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-x-circle"></i>
                        </a>
                    @endif
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Order #</th>
                        <th>Customer</th>
                        <th>Event</th>
                        <th>Event Date</th>
                        <th>Guests</th>
                        <th>Amount</th>
                        <th>Payment</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($cateringOrders as $order)
                        <tr>
                            <td>
                                <div class="fw-bold">{{ $order->order_number }}</div>
                                @if($order->notes)
                                    <small class="text-muted">
                                        <i class="bi bi-chat-left-text"></i>
                                        {{ Str::limit($order->notes, 30) }}
                                    </small>
                                @endif
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ $order->customer_name }}</div>
                                    <small class="text-muted">{{ $order->customer_email }}</small>
                                    @if($order->customer_phone)
                                        <br><small class="text-muted">{{ $order->customer_phone }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ $order->eventType->name ?? 'N/A' }}</div>
                                    @if($order->package)
                                        <small class="text-muted">{{ $order->package->name }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>{{ $order->event_date->format('M d, Y') }}</div>
                                @if($order->event_start_time)
                                    <small class="text-muted">{{ $order->event_start_time->format('H:i') }}</small>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $order->guest_count }} guests</span>
                            </td>
                            <td>
                                <div class="fw-bold">${{ number_format($order->total_amount, 2) }}</div>
                                @if(isset($order->deposit_amount) && $order->deposit_amount > 0)
                                    <small class="text-muted">
                                        Deposit: ${{ number_format($order->deposit_amount, 2) }}
                                    </small>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{ $order->payment_status === 'paid' ? 'success' : ($order->payment_status === 'failed' ? 'danger' : 'warning') }}">
                                    {{ ucfirst($order->payment_status) }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ $order->status === 'completed' || $order->status === 'delivered' ? 'success' : ($order->status === 'cancelled' ? 'danger' : 'warning') }}">
                                    {{ ucfirst(str_replace('_', ' ', $order->status)) }}
                                </span>
                            </td>
                            <td>
                                <div>{{ $order->created_at->format('M d, Y') }}</div>
                                <small class="text-muted">{{ $order->created_at->format('H:i') }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ route('admin.catering-orders.show', $order) }}" class="btn btn-outline-info" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.catering-orders.invoice', $order) }}" class="btn btn-outline-secondary" title="Print Invoice" target="_blank">
                                        <i class="bi bi-printer"></i>
                                    </a>
                                    @if(!in_array($order->status, ['completed', 'cancelled']))
                                        <button type="button" class="btn btn-outline-primary" title="Update Status" 
                                                data-bs-toggle="modal" data-bs-target="#statusModal{{ $order->id }}">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>

                        <!-- Status Update Modal -->
                        <div class="modal fade" id="statusModal{{ $order->id }}" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form method="POST" action="{{ route('admin.catering-orders.update-status', $order) }}">
                                        @csrf
                                        @method('PATCH')
                                        <div class="modal-header">
                                            <h5 class="modal-title">Update Catering Order Status</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <label class="form-label">Order Number</label>
                                                <input type="text" class="form-control" value="{{ $order->order_number }}" readonly>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Current Status</label>
                                                <input type="text" class="form-control" value="{{ ucfirst(str_replace('_', ' ', $order->status)) }}" readonly>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">New Status</label>
                                                <select name="status" class="form-select" required>
                                                    <option value="pending" {{ $order->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                                    <option value="confirmed" {{ $order->status === 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                                                    <option value="in_preparation" {{ $order->status === 'in_preparation' ? 'selected' : '' }}>In Preparation</option>
                                                    <option value="ready" {{ $order->status === 'ready' ? 'selected' : '' }}>Ready</option>
                                                    <option value="in_transit" {{ $order->status === 'in_transit' ? 'selected' : '' }}>In Transit</option>
                                                    <option value="delivered" {{ $order->status === 'delivered' ? 'selected' : '' }}>Delivered</option>
                                                    <option value="completed" {{ $order->status === 'completed' ? 'selected' : '' }}>Completed</option>
                                                    <option value="cancelled" {{ $order->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Notes (Optional)</label>
                                                <textarea name="notes" class="form-control" rows="3" placeholder="Add any notes about this status update..."></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-primary">Update Status</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @empty
                        <tr>
                            <td colspan="10" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-calendar-event fs-1 d-block mb-2"></i>
                                    No catering orders found
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    @if($cateringOrders->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Showing {{ $cateringOrders->firstItem() }} to {{ $cateringOrders->lastItem() }} of {{ $cateringOrders->total() }} results
                </div>
                {{ $cateringOrders->links() }}
            </div>
        </div>
    @endif
</div>
@endsection
