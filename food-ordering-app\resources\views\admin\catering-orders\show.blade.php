@extends('admin.layouts.app')

@section('title', 'Catering Order Details')
@section('page-title', 'Catering Order Details')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('admin.catering-orders.index') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Catering Orders
    </a>
    <a href="{{ route('admin.catering-orders.invoice', $cateringOrder) }}" class="btn btn-outline-primary" target="_blank">
        <i class="bi bi-printer me-1"></i>
        Print Invoice
    </a>
    @if(!in_array($cateringOrder->status, ['completed', 'cancelled']))
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#statusModal">
            <i class="bi bi-pencil me-1"></i>
            Update Status
        </button>
    @endif
    @if($cateringOrder->payment_status !== 'paid')
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#paymentModal">
            <i class="bi bi-credit-card me-1"></i>
            Update Payment
        </button>
    @endif
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8">
        <!-- Order Information -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Catering Order #{{ $cateringOrder->order_number }}</h5>
                    <div>
                        <span class="badge bg-{{ $cateringOrder->status === 'completed' || $cateringOrder->status === 'delivered' ? 'success' : ($cateringOrder->status === 'cancelled' ? 'danger' : 'warning') }} fs-6">
                            {{ ucfirst(str_replace('_', ' ', $cateringOrder->status)) }}
                        </span>
                        <span class="badge bg-{{ $cateringOrder->payment_status === 'paid' ? 'success' : ($cateringOrder->payment_status === 'failed' ? 'danger' : 'warning') }} fs-6">
                            {{ ucfirst($cateringOrder->payment_status) }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Customer Information</h6>
                        <div class="mb-2">
                            <strong>Name:</strong> {{ $cateringOrder->customer_name }}
                        </div>
                        <div class="mb-2">
                            <strong>Email:</strong> {{ $cateringOrder->customer_email }}
                        </div>
                        @if($cateringOrder->customer_phone)
                            <div class="mb-2">
                                <strong>Phone:</strong> {{ $cateringOrder->customer_phone }}
                            </div>
                        @endif
                        @if($cateringOrder->user)
                            <div class="mb-2">
                                <strong>Account:</strong> 
                                <a href="{{ route('admin.users.show', $cateringOrder->user) }}" class="text-decoration-none">
                                    Registered User
                                </a>
                            </div>
                        @endif
                    </div>
                    
                    <div class="col-md-6">
                        <h6>Event Details</h6>
                        <div class="mb-2">
                            <strong>Event Type:</strong> {{ $cateringOrder->eventType->name ?? 'N/A' }}
                        </div>
                        <div class="mb-2">
                            <strong>Event Date:</strong> {{ $cateringOrder->event_date->format('M d, Y') }}
                        </div>
                        @if($cateringOrder->event_start_time)
                            <div class="mb-2">
                                <strong>Event Time:</strong> {{ $cateringOrder->event_start_time->format('H:i') }}
                                @if($cateringOrder->event_end_time)
                                    - {{ $cateringOrder->event_end_time->format('H:i') }}
                                @endif
                            </div>
                        @endif
                        <div class="mb-2">
                            <strong>Guest Count:</strong> {{ $cateringOrder->guest_count }} guests
                        </div>
                        <div class="mb-2">
                            <strong>Order Date:</strong> {{ $cateringOrder->created_at->format('M d, Y H:i A') }}
                        </div>
                    </div>
                </div>
                
                @if($cateringOrder->event_address)
                    <hr>
                    <h6>Event Address</h6>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-2">
                                <strong>Address:</strong> {{ $cateringOrder->event_address }}
                            </div>
                            @if($cateringOrder->event_city)
                                <div class="mb-2">
                                    <strong>City:</strong> {{ $cateringOrder->event_city }}
                                </div>
                            @endif
                            @if($cateringOrder->event_postal_code)
                                <div class="mb-2">
                                    <strong>Postal Code:</strong> {{ $cateringOrder->event_postal_code }}
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
                
                @if($cateringOrder->special_requests)
                    <hr>
                    <h6>Special Requests</h6>
                    <p class="text-muted">{{ $cateringOrder->special_requests }}</p>
                @endif
                
                @if($cateringOrder->notes)
                    <hr>
                    <h6>Order Notes</h6>
                    <p class="text-muted">{{ $cateringOrder->notes }}</p>
                @endif
            </div>
        </div>
        
        <!-- Package & Items -->
        @if($cateringOrder->package)
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Selected Package</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            @if($cateringOrder->package->image)
                                <img src="{{ Storage::url($cateringOrder->package->image) }}" alt="{{ $cateringOrder->package->name }}" 
                                     class="img-fluid rounded" style="width: 100%; height: 200px; object-fit: cover;">
                            @else
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="width: 100%; height: 200px;">
                                    <i class="bi bi-box text-muted fs-1"></i>
                                </div>
                            @endif
                        </div>
                        <div class="col-md-8">
                            <h5>{{ $cateringOrder->package->name }}</h5>
                            <p class="text-muted">{{ $cateringOrder->package->description }}</p>
                            <div class="row">
                                <div class="col-sm-6">
                                    <strong>Price per Person:</strong> ${{ number_format($cateringOrder->package->price_per_person, 2) }}
                                </div>
                                <div class="col-sm-6">
                                    <strong>Minimum Guests:</strong> {{ $cateringOrder->package->min_guests }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        
        <!-- Order Items -->
        @if($cateringOrder->orderItems && $cateringOrder->orderItems->count() > 0)
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Additional Items</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Item</th>
                                    <th>Unit Price</th>
                                    <th>Quantity</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($cateringOrder->orderItems as $item)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($item->foodItem && $item->foodItem->image)
                                                    <img src="{{ Storage::url($item->foodItem->image) }}" alt="{{ $item->item_name }}" 
                                                         class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                                @else
                                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                         style="width: 50px; height: 50px;">
                                                        <i class="bi bi-image text-muted"></i>
                                                    </div>
                                                @endif
                                                <div>
                                                    <div class="fw-bold">{{ $item->item_name }}</div>
                                                    @if($item->foodItem)
                                                        <small class="text-muted">
                                                            <a href="{{ route('admin.food-items.show', $item->foodItem) }}" class="text-decoration-none">
                                                                View Item Details
                                                            </a>
                                                        </small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>${{ number_format($item->unit_price, 2) }}</td>
                                        <td>{{ $item->quantity }} {{ $item->unit }}</td>
                                        <td class="fw-bold">${{ number_format($item->total_price, 2) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif
    </div>
    
    <div class="col-lg-4">
        <!-- Order Summary -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Order Summary</h6>
            </div>
            <div class="card-body">
                @if($cateringOrder->package)
                    <div class="d-flex justify-content-between mb-2">
                        <span>Package ({{ $cateringOrder->guest_count }} guests):</span>
                        <span>${{ number_format($cateringOrder->package->price_per_person * $cateringOrder->guest_count, 2) }}</span>
                    </div>
                @endif
                
                @if($cateringOrder->orderItems && $cateringOrder->orderItems->count() > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>Additional Items:</span>
                        <span>${{ number_format($cateringOrder->orderItems->sum('total_price'), 2) }}</span>
                    </div>
                @endif
                
                @if($cateringOrder->service_fee > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>Service Fee:</span>
                        <span>${{ number_format($cateringOrder->service_fee, 2) }}</span>
                    </div>
                @endif
                
                @if($cateringOrder->tax_amount > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax:</span>
                        <span>${{ number_format($cateringOrder->tax_amount, 2) }}</span>
                    </div>
                @endif
                
                @if($cateringOrder->discount_amount > 0)
                    <div class="d-flex justify-content-between mb-2 text-success">
                        <span>Discount:</span>
                        <span>-${{ number_format($cateringOrder->discount_amount, 2) }}</span>
                    </div>
                @endif
                
                <hr>
                
                <div class="d-flex justify-content-between fw-bold">
                    <span>Total:</span>
                    <span class="text-primary">${{ number_format($cateringOrder->total_amount, 2) }}</span>
                </div>
                
                @if(isset($cateringOrder->deposit_amount) && $cateringOrder->deposit_amount > 0)
                    <hr>
                    <div class="d-flex justify-content-between">
                        <span>Deposit Paid:</span>
                        <span class="text-success">${{ number_format($cateringOrder->deposit_amount, 2) }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Balance Due:</span>
                        <span class="text-warning">${{ number_format($cateringOrder->total_amount - $cateringOrder->deposit_amount, 2) }}</span>
                    </div>
                @endif
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.catering-orders.invoice', $cateringOrder) }}" class="btn btn-outline-primary" target="_blank">
                        <i class="bi bi-printer me-1"></i>
                        Print Invoice
                    </a>
                    
                    @if(!in_array($cateringOrder->status, ['completed', 'cancelled']))
                        <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#statusModal">
                            <i class="bi bi-pencil me-1"></i>
                            Update Status
                        </button>
                    @endif
                    
                    @if($cateringOrder->payment_status !== 'paid')
                        <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#paymentModal">
                            <i class="bi bi-credit-card me-1"></i>
                            Update Payment
                        </button>
                    @endif
                    
                    @if($cateringOrder->user)
                        <a href="{{ route('admin.users.show', $cateringOrder->user) }}" class="btn btn-outline-info">
                            <i class="bi bi-person me-1"></i>
                            View Customer
                        </a>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Order Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Order Info</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <strong>Order ID:</strong><br>
                        {{ $cateringOrder->id }}
                    </div>
                    <div class="mb-2">
                        <strong>Event Date:</strong><br>
                        {{ $cateringOrder->event_date->format('M d, Y') }}
                    </div>
                    <div class="mb-2">
                        <strong>Days Until Event:</strong><br>
                        {{ $cateringOrder->event_date->diffInDays(now()) }} days
                    </div>
                    <div>
                        <strong>Last Updated:</strong><br>
                        {{ $cateringOrder->updated_at->format('M d, Y H:i A') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.catering-orders.update-status', $cateringOrder) }}">
                @csrf
                @method('PATCH')
                <div class="modal-header">
                    <h5 class="modal-title">Update Catering Order Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Current Status</label>
                        <input type="text" class="form-control" value="{{ ucfirst(str_replace('_', ' ', $cateringOrder->status)) }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">New Status</label>
                        <select name="status" class="form-select" required>
                            <option value="pending" {{ $cateringOrder->status === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="confirmed" {{ $cateringOrder->status === 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                            <option value="in_preparation" {{ $cateringOrder->status === 'in_preparation' ? 'selected' : '' }}>In Preparation</option>
                            <option value="ready" {{ $cateringOrder->status === 'ready' ? 'selected' : '' }}>Ready</option>
                            <option value="in_transit" {{ $cateringOrder->status === 'in_transit' ? 'selected' : '' }}>In Transit</option>
                            <option value="delivered" {{ $cateringOrder->status === 'delivered' ? 'selected' : '' }}>Delivered</option>
                            <option value="completed" {{ $cateringOrder->status === 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="cancelled" {{ $cateringOrder->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes (Optional)</label>
                        <textarea name="notes" class="form-control" rows="3" placeholder="Add any notes about this status update..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Payment Status Update Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.catering-orders.update-payment-status', $cateringOrder) }}">
                @csrf
                @method('PATCH')
                <div class="modal-header">
                    <h5 class="modal-title">Update Payment Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Current Payment Status</label>
                        <input type="text" class="form-control" value="{{ ucfirst($cateringOrder->payment_status) }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">New Payment Status</label>
                        <select name="payment_status" class="form-select" required>
                            <option value="pending" {{ $cateringOrder->payment_status === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="paid" {{ $cateringOrder->payment_status === 'paid' ? 'selected' : '' }}>Paid</option>
                            <option value="failed" {{ $cateringOrder->payment_status === 'failed' ? 'selected' : '' }}>Failed</option>
                            <option value="refunded" {{ $cateringOrder->payment_status === 'refunded' ? 'selected' : '' }}>Refunded</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Update Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
