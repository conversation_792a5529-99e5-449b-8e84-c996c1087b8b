@extends('admin.layouts.app')

@section('title', 'Food Item Details')
@section('page-title', 'Food Item Details')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('admin.food-items.index') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Food Items
    </a>
    <a href="{{ route('admin.food-items.edit', $foodItem) }}" class="btn btn-primary">
        <i class="bi bi-pencil me-1"></i>
        Edit Food Item
    </a>
    <form method="POST" action="{{ route('admin.food-items.toggle-availability', $foodItem) }}" class="d-inline">
        @csrf
        @method('PATCH')
        <button type="submit" class="btn btn-{{ $foodItem->is_available ? 'warning' : 'success' }}">
            <i class="bi bi-{{ $foodItem->is_available ? 'pause' : 'play' }} me-1"></i>
            {{ $foodItem->is_available ? 'Mark Unavailable' : 'Mark Available' }}
        </button>
    </form>
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ $foodItem->name }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        @if($foodItem->image)
                            <img src="{{ Storage::url($foodItem->image) }}" alt="{{ $foodItem->name }}" 
                                 class="img-fluid rounded mb-3" style="width: 100%; height: 250px; object-fit: cover;">
                        @else
                            <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3" 
                                 style="width: 100%; height: 250px;">
                                <i class="bi bi-image text-muted fs-1"></i>
                            </div>
                        @endif
                    </div>
                    
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Name</label>
                                <p class="form-control-plaintext">{{ $foodItem->name }}</p>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Status</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-{{ $foodItem->is_available ? 'success' : 'secondary' }} fs-6">
                                        {{ $foodItem->is_available ? 'Available' : 'Unavailable' }}
                                    </span>
                                </p>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Category</label>
                                <p class="form-control-plaintext">{{ $foodItem->category->name ?? 'Not assigned' }}</p>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Cuisine</label>
                                <p class="form-control-plaintext">{{ $foodItem->cuisine->name ?? 'Not assigned' }}</p>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Price per {{ $foodItem->unit }}</label>
                                <p class="form-control-plaintext">
                                    <span class="h5 text-primary">${{ number_format($foodItem->price_per_unit, 2) }}</span>
                                </p>
                            </div>
                            
                            @if($foodItem->price_per_kg)
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Price per KG</label>
                                    <p class="form-control-plaintext">
                                        <span class="h6 text-success">${{ number_format($foodItem->price_per_kg, 2) }}</span>
                                    </p>
                                </div>
                            @endif
                            
                            @if($foodItem->stock_quantity !== null)
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Stock Quantity</label>
                                    <p class="form-control-plaintext">
                                        <span class="badge bg-{{ $foodItem->stock_quantity > 10 ? 'success' : ($foodItem->stock_quantity > 0 ? 'warning' : 'danger') }} fs-6">
                                            {{ $foodItem->stock_quantity }} {{ $foodItem->unit }}s
                                        </span>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <label class="form-label fw-bold">Description</label>
                        <p class="form-control-plaintext">{{ $foodItem->description }}</p>
                    </div>
                    
                    @if($foodItem->ingredients)
                        <div class="col-12">
                            <label class="form-label fw-bold">Ingredients</label>
                            <p class="form-control-plaintext">{{ $foodItem->ingredients }}</p>
                        </div>
                    @endif
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Dietary Information</h6>
                        <div class="d-flex flex-wrap gap-2">
                            @if($foodItem->is_vegetarian)
                                <span class="badge bg-success">Vegetarian</span>
                            @endif
                            @if($foodItem->is_vegan)
                                <span class="badge bg-success">Vegan</span>
                            @endif
                            @if($foodItem->is_gluten_free)
                                <span class="badge bg-info">Gluten Free</span>
                            @endif
                            @if($foodItem->is_spicy)
                                <span class="badge bg-warning">Spicy</span>
                                @if($foodItem->spice_level)
                                    <span class="badge bg-danger">Level {{ $foodItem->spice_level }}</span>
                                @endif
                            @endif
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>Features</h6>
                        <div class="d-flex flex-wrap gap-2">
                            @if($foodItem->is_popular)
                                <span class="badge bg-danger">
                                    <i class="bi bi-fire me-1"></i>Popular
                                </span>
                            @endif
                            @if($foodItem->is_featured)
                                <span class="badge bg-warning">
                                    <i class="bi bi-star-fill me-1"></i>Featured
                                </span>
                            @endif
                            @if($foodItem->allow_bulk_order)
                                <span class="badge bg-primary">Bulk Orders Allowed</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Order History</h5>
            </div>
            <div class="card-body">
                @if($foodItem->orderItems && $foodItem->orderItems->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Quantity</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($foodItem->orderItems->take(10) as $orderItem)
                                    <tr>
                                        <td>{{ $orderItem->order->order_number }}</td>
                                        <td>{{ $orderItem->order->customer_name }}</td>
                                        <td>{{ $orderItem->order->created_at->format('M d, Y') }}</td>
                                        <td>{{ $orderItem->quantity }} {{ $orderItem->unit }}</td>
                                        <td>${{ number_format($orderItem->total_price, 2) }}</td>
                                        <td>
                                            <span class="badge bg-{{ $orderItem->order->status === 'completed' ? 'success' : ($orderItem->order->status === 'cancelled' ? 'danger' : 'warning') }}">
                                                {{ ucfirst($orderItem->order->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.orders.show', $orderItem->order) }}" class="btn btn-sm btn-outline-primary">
                                                View
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if($foodItem->orderItems->count() > 10)
                        <div class="text-center mt-3">
                            <a href="{{ route('admin.orders.index', ['food_item' => $foodItem->id]) }}" class="btn btn-outline-primary">
                                View All Orders ({{ $foodItem->orderItems->count() }})
                            </a>
                        </div>
                    @endif
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-receipt fs-1 text-muted"></i>
                        <p class="text-muted mt-2">No orders for this item yet</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Quick Stats</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-0">{{ $foodItem->orderItems ? $foodItem->orderItems->count() : 0 }}</h4>
                            <small class="text-muted">Total Orders</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-0">{{ $foodItem->orderItems ? $foodItem->orderItems->sum('quantity') : 0 }}</h4>
                        <small class="text-muted">Units Sold</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-12">
                        <h4 class="text-info mb-0">${{ $foodItem->orderItems ? number_format($foodItem->orderItems->sum('total_price'), 2) : '0.00' }}</h4>
                        <small class="text-muted">Total Revenue</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Item Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.food-items.edit', $foodItem) }}" class="btn btn-outline-primary">
                        <i class="bi bi-pencil me-1"></i>
                        Edit Food Item
                    </a>
                    
                    <form method="POST" action="{{ route('admin.food-items.toggle-availability', $foodItem) }}">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="btn btn-outline-{{ $foodItem->is_available ? 'warning' : 'success' }} w-100">
                            <i class="bi bi-{{ $foodItem->is_available ? 'pause' : 'play' }} me-1"></i>
                            {{ $foodItem->is_available ? 'Mark Unavailable' : 'Mark Available' }}
                        </button>
                    </form>
                    
                    <form method="POST" action="{{ route('admin.food-items.destroy', $foodItem) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this food item? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="bi bi-trash me-1"></i>
                            Delete Food Item
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Item Details</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <strong>Created:</strong><br>
                        {{ $foodItem->created_at->format('M d, Y H:i A') }}
                    </div>
                    <div class="mb-2">
                        <strong>Last Updated:</strong><br>
                        {{ $foodItem->updated_at->format('M d, Y H:i A') }}
                    </div>
                    <div>
                        <strong>Slug:</strong><br>
                        <code>{{ $foodItem->slug }}</code>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
