@extends('admin.layouts.app')

@section('title', 'Cuisines Management')
@section('page-title', 'Cuisines Management')

@section('page-actions')
<a href="{{ route('admin.cuisines.create') }}" class="btn btn-primary">
    <i class="bi bi-plus-circle me-1"></i>
    Add New Cuisine
</a>
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">All Cuisines</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="{{ route('admin.cuisines.index') }}" class="d-flex gap-2">
                    <input type="text" name="search" class="form-control form-control-sm" 
                           placeholder="Search cuisines..." value="{{ request('search') }}">
                    
                    <select name="status" class="form-select form-select-sm">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                    
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-search"></i>
                    </button>
                    
                    @if(request()->hasAny(['search', 'status']))
                        <a href="{{ route('admin.cuisines.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-x-circle"></i>
                        </a>
                    @endif
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bulk Actions -->
    <div class="card-body border-bottom">
        <form id="bulkActionForm" method="POST" action="{{ route('admin.cuisines.bulk-action') }}">
            @csrf
            <div class="row align-items-center">
                <div class="col-auto">
                    <input type="checkbox" id="selectAll" class="form-check-input">
                    <label for="selectAll" class="form-check-label">Select All</label>
                </div>
                <div class="col-auto">
                    <select name="action" class="form-select form-select-sm" required>
                        <option value="">Bulk Actions</option>
                        <option value="activate">Activate</option>
                        <option value="deactivate">Deactivate</option>
                        <option value="delete">Delete</option>
                    </select>
                </div>
                <div class="col-auto">
                    <button type="submit" class="btn btn-outline-primary btn-sm">Apply</button>
                </div>
            </div>
        </form>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="40">
                            <input type="checkbox" class="form-check-input" id="selectAllHeader">
                        </th>
                        <th>Image</th>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Food Items</th>
                        <th>Sort Order</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($cuisines as $cuisine)
                        <tr>
                            <td>
                                <input type="checkbox" name="items[]" value="{{ $cuisine->id }}" class="form-check-input item-checkbox">
                            </td>
                            <td>
                                @if($cuisine->image)
                                    <img src="{{ Storage::url($cuisine->image) }}" alt="{{ $cuisine->name }}" 
                                         class="rounded" width="50" height="50" style="object-fit: cover;">
                                @else
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                         style="width: 50px; height: 50px;">
                                        <i class="bi bi-image text-muted"></i>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ $cuisine->name }}</div>
                                    <small class="text-muted">{{ $cuisine->slug }}</small>
                                </div>
                            </td>
                            <td>
                                <div>{{ Str::limit($cuisine->description, 60) ?: '-' }}</div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $cuisine->food_items_count ?? 0 }}</span>
                            </td>
                            <td>{{ $cuisine->sort_order ?? '-' }}</td>
                            <td>
                                <span class="badge bg-{{ $cuisine->is_active ? 'success' : 'secondary' }}">
                                    {{ $cuisine->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ route('admin.cuisines.show', $cuisine) }}" class="btn btn-outline-info" title="View">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.cuisines.edit', $cuisine) }}" class="btn btn-outline-primary" title="Edit">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    
                                    <form method="POST" action="{{ route('admin.cuisines.toggle-status', $cuisine) }}" class="d-inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="btn btn-outline-{{ $cuisine->is_active ? 'warning' : 'success' }}" 
                                                title="{{ $cuisine->is_active ? 'Deactivate' : 'Activate' }}">
                                            <i class="bi bi-{{ $cuisine->is_active ? 'pause' : 'play' }}"></i>
                                        </button>
                                    </form>
                                    
                                    <form method="POST" action="{{ route('admin.cuisines.destroy', $cuisine) }}" class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this cuisine?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-outline-danger" title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-globe fs-1 d-block mb-2"></i>
                                    No cuisines found
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    @if($cuisines->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Showing {{ $cuisines->firstItem() }} to {{ $cuisines->lastItem() }} of {{ $cuisines->total() }} results
                </div>
                {{ $cuisines->links() }}
            </div>
        </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
// Select All functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

document.getElementById('selectAllHeader').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Bulk action form submission
document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
    const selectedItems = document.querySelectorAll('.item-checkbox:checked');
    if (selectedItems.length === 0) {
        e.preventDefault();
        alert('Please select at least one item.');
        return;
    }
    
    const action = document.querySelector('select[name="action"]').value;
    if (!action) {
        e.preventDefault();
        alert('Please select an action.');
        return;
    }
    
    if (action === 'delete') {
        if (!confirm('Are you sure you want to delete the selected cuisines?')) {
            e.preventDefault();
        }
    }
});
</script>
@endpush
