@extends('admin.layouts.app')

@section('title', 'Create Food Item')
@section('page-title', 'Create New Food Item')

@section('page-actions')
<a href="{{ route('admin.food-items.index') }}" class="btn btn-secondary">
    <i class="bi bi-arrow-left me-1"></i>
    Back to Food Items
</a>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Food Item Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.food-items.store') }}" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Food Item Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="image" class="form-label">Food Image</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Max size: 2MB. Formats: JPEG, PNG, JPG, GIF</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="ingredients" class="form-label">Ingredients</label>
                        <textarea class="form-control @error('ingredients') is-invalid @enderror" 
                                  id="ingredients" name="ingredients" rows="3">{{ old('ingredients') }}</textarea>
                        @error('ingredients')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">List main ingredients separated by commas</small>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="price_per_unit" class="form-label">Price per Unit <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control @error('price_per_unit') is-invalid @enderror" 
                                       id="price_per_unit" name="price_per_unit" value="{{ old('price_per_unit') }}" 
                                       step="0.01" min="0" required>
                                @error('price_per_unit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="price_per_kg" class="form-label">Price per KG</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control @error('price_per_kg') is-invalid @enderror" 
                                       id="price_per_kg" name="price_per_kg" value="{{ old('price_per_kg') }}" 
                                       step="0.01" min="0">
                                @error('price_per_kg')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="unit" class="form-label">Unit <span class="text-danger">*</span></label>
                            <select class="form-select @error('unit') is-invalid @enderror" id="unit" name="unit" required>
                                <option value="">Select Unit</option>
                                <option value="piece" {{ old('unit') === 'piece' ? 'selected' : '' }}>Piece</option>
                                <option value="plate" {{ old('unit') === 'plate' ? 'selected' : '' }}>Plate</option>
                                <option value="bowl" {{ old('unit') === 'bowl' ? 'selected' : '' }}>Bowl</option>
                                <option value="cup" {{ old('unit') === 'cup' ? 'selected' : '' }}>Cup</option>
                                <option value="kg" {{ old('unit') === 'kg' ? 'selected' : '' }}>Kilogram</option>
                                <option value="gram" {{ old('unit') === 'gram' ? 'selected' : '' }}>Gram</option>
                                <option value="liter" {{ old('unit') === 'liter' ? 'selected' : '' }}>Liter</option>
                                <option value="ml" {{ old('unit') === 'ml' ? 'selected' : '' }}>Milliliter</option>
                            </select>
                            @error('unit')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                            <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="cuisine_id" class="form-label">Cuisine <span class="text-danger">*</span></label>
                            <select class="form-select @error('cuisine_id') is-invalid @enderror" id="cuisine_id" name="cuisine_id" required>
                                <option value="">Select Cuisine</option>
                                @foreach($cuisines as $cuisine)
                                    <option value="{{ $cuisine->id }}" {{ old('cuisine_id') == $cuisine->id ? 'selected' : '' }}>
                                        {{ $cuisine->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('cuisine_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="stock_quantity" class="form-label">Stock Quantity</label>
                            <input type="number" class="form-control @error('stock_quantity') is-invalid @enderror" 
                                   id="stock_quantity" name="stock_quantity" value="{{ old('stock_quantity') }}" min="0">
                            @error('stock_quantity')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Leave empty for unlimited stock</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="spice_level" class="form-label">Spice Level</label>
                            <select class="form-select @error('spice_level') is-invalid @enderror" id="spice_level" name="spice_level">
                                <option value="">Not Applicable</option>
                                <option value="1" {{ old('spice_level') == '1' ? 'selected' : '' }}>1 - Mild</option>
                                <option value="2" {{ old('spice_level') == '2' ? 'selected' : '' }}>2 - Medium</option>
                                <option value="3" {{ old('spice_level') == '3' ? 'selected' : '' }}>3 - Hot</option>
                                <option value="4" {{ old('spice_level') == '4' ? 'selected' : '' }}>4 - Very Hot</option>
                                <option value="5" {{ old('spice_level') == '5' ? 'selected' : '' }}>5 - Extremely Hot</option>
                            </select>
                            @error('spice_level')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Dietary Information</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_vegetarian" name="is_vegetarian" 
                                       {{ old('is_vegetarian') ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_vegetarian">Vegetarian</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_vegan" name="is_vegan" 
                                       {{ old('is_vegan') ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_vegan">Vegan</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_gluten_free" name="is_gluten_free" 
                                       {{ old('is_gluten_free') ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_gluten_free">Gluten Free</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_spicy" name="is_spicy" 
                                       {{ old('is_spicy') ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_spicy">Spicy</label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>Status & Features</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_available" name="is_available" 
                                       {{ old('is_available', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_available">Available for Order</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_popular" name="is_popular" 
                                       {{ old('is_popular') ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_popular">Popular Item</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                       {{ old('is_featured') ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_featured">Featured Item</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="allow_bulk_order" name="allow_bulk_order" 
                                       {{ old('allow_bulk_order') ? 'checked' : '' }}>
                                <label class="form-check-label" for="allow_bulk_order">Allow Bulk Orders</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>
                            Create Food Item
                        </button>
                        <a href="{{ route('admin.food-items.index') }}" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Guidelines</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">Image Guidelines</h6>
                    <ul class="mb-0 small">
                        <li>Use high-quality images (minimum 300x300px)</li>
                        <li>Square aspect ratio works best</li>
                        <li>Show the food clearly and appetizingly</li>
                        <li>Maximum file size: 2MB</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6 class="alert-heading">Pricing Tips</h6>
                    <ul class="mb-0 small">
                        <li>Price per unit is required</li>
                        <li>Price per KG is optional for bulk items</li>
                        <li>Consider competitor pricing</li>
                        <li>Include preparation costs</li>
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <h6 class="alert-heading">SEO Tips</h6>
                    <ul class="mb-0 small">
                        <li>Use descriptive, appetizing names</li>
                        <li>Include key ingredients in description</li>
                        <li>Mention dietary restrictions clearly</li>
                        <li>Add cooking method if relevant</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
