<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class FoodItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'ingredients',
        'image',
        'images',
        'price_per_unit',
        'price_per_kg',
        'unit',
        'food_type',
        'is_vegan',
        'is_gluten_free',
        'is_spicy',
        'spice_level',
        'is_available',
        'is_popular',
        'is_featured',
        'allow_bulk_order',
        'calories_per_unit',
        'protein',
        'carbs',
        'fat',
        'preparation_time',
        'preparation_notes',
        'sort_order',
        'stock_quantity',
        'min_order_quantity',
        'max_order_quantity',
    ];

    protected $casts = [
        'images' => 'array',
        'is_vegan' => 'boolean',
        'is_gluten_free' => 'boolean',
        'is_spicy' => 'boolean',
        'is_available' => 'boolean',
        'is_popular' => 'boolean',
        'is_featured' => 'boolean',
        'allow_bulk_order' => 'boolean',
        'price_per_unit' => 'decimal:2',
        'price_per_kg' => 'decimal:2',
        'protein' => 'decimal:2',
        'carbs' => 'decimal:2',
        'fat' => 'decimal:2',
    ];

    /**
     * Get the categories for this food item (many-to-many).
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'food_item_categories')
                    ->withPivot('is_primary', 'sort_order')
                    ->withTimestamps()
                    ->orderByPivot('sort_order')
                    ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get the primary category for this food item.
     */
    public function primaryCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    /**
     * Get the cuisines for this food item (many-to-many).
     */
    public function cuisines(): BelongsToMany
    {
        return $this->belongsToMany(Cuisine::class, 'food_item_cuisines')
                    ->withPivot('is_primary', 'sort_order')
                    ->withTimestamps()
                    ->orderByPivot('sort_order')
                    ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get the primary cuisine for this food item.
     */
    public function primaryCuisine(): BelongsTo
    {
        return $this->belongsTo(Cuisine::class, 'cuisine_id');
    }

    /**
     * Get the meal times for this food item (many-to-many).
     */
    public function mealTimes(): BelongsToMany
    {
        return $this->belongsToMany(MealTime::class, 'food_item_meal_times')
                    ->withPivot('is_primary', 'sort_order')
                    ->withTimestamps()
                    ->orderByPivot('sort_order')
                    ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get the cart items for this food item.
     */
    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Get the order items for this food item.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the packages that contain this food item.
     */
    public function packages(): BelongsToMany
    {
        return $this->belongsToMany(Package::class, 'package_items')
                    ->withPivot('quantity', 'unit_weight', 'notes', 'is_optional', 'is_customizable', 'sort_order')
                    ->withTimestamps();
    }

    /**
     * Scope to get only available items.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope to filter by food type.
     */
    public function scopeByFoodType($query, $foodType)
    {
        return $query->where('food_type', $foodType);
    }

    /**
     * Scope to get only vegetarian items.
     */
    public function scopeVegetarian($query)
    {
        return $query->where('food_type', 'veg');
    }

    /**
     * Scope to get only non-vegetarian items.
     */
    public function scopeNonVegetarian($query)
    {
        return $query->where('food_type', 'non_veg');
    }

    /**
     * Scope to get only popular items.
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Scope to get only featured items.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to filter by meal time.
     */
    public function scopeByMealTime($query, $mealTimeId)
    {
        return $query->whereHas('mealTimes', function ($q) use ($mealTimeId) {
            $q->where('meal_time_id', $mealTimeId);
        });
    }

    /**
     * Scope to filter by multiple meal times.
     */
    public function scopeByMealTimes($query, array $mealTimeIds)
    {
        return $query->whereHas('mealTimes', function ($q) use ($mealTimeIds) {
            $q->whereIn('meal_time_id', $mealTimeIds);
        });
    }

    /**
     * Scope to filter by category (many-to-many).
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->whereHas('categories', function ($q) use ($categoryId) {
            $q->where('category_id', $categoryId);
        });
    }

    /**
     * Scope to filter by multiple categories.
     */
    public function scopeByCategories($query, array $categoryIds)
    {
        return $query->whereHas('categories', function ($q) use ($categoryIds) {
            $q->whereIn('category_id', $categoryIds);
        });
    }

    /**
     * Scope to filter by cuisine (many-to-many).
     */
    public function scopeByCuisine($query, $cuisineId)
    {
        return $query->whereHas('cuisines', function ($q) use ($cuisineId) {
            $q->where('cuisine_id', $cuisineId);
        });
    }

    /**
     * Scope to filter by multiple cuisines.
     */
    public function scopeByCuisines($query, array $cuisineIds)
    {
        return $query->whereHas('cuisines', function ($q) use ($cuisineIds) {
            $q->whereIn('cuisine_id', $cuisineIds);
        });
    }

    /**
     * Scope to search by name or description.
     */
    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('description', 'like', "%{$term}%")
              ->orWhere('ingredients', 'like', "%{$term}%");
        });
    }

    /**
     * Get the effective price based on order type.
     */
    public function getEffectivePrice($orderType = 'unit', $weight = null)
    {
        if ($orderType === 'bulk' && $this->allow_bulk_order && $this->price_per_kg) {
            return $this->price_per_kg * ($weight ?? 1);
        }
        
        return $this->price_per_unit;
    }
}
