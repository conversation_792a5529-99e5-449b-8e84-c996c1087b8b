<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Cuisine extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'image',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the food items for this cuisine (many-to-many).
     */
    public function foodItems(): BelongsToMany
    {
        return $this->belongsToMany(FoodItem::class, 'food_item_cuisines')
                    ->withPivot('is_primary', 'sort_order')
                    ->withTimestamps()
                    ->orderByPivot('sort_order')
                    ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get the categories for this cuisine.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'cuisine_categories')
                    ->withPivot('is_featured', 'sort_order')
                    ->withTimestamps()
                    ->orderByPivot('sort_order');
    }

    /**
     * Get the packages for this cuisine.
     */
    public function packages(): HasMany
    {
        return $this->hasMany(Package::class);
    }

    /**
     * Get active food items for this cuisine.
     */
    public function activeFoodItems(): BelongsToMany
    {
        return $this->foodItems()->where('food_items.is_available', true);
    }



    /**
     * Get active packages for this cuisine.
     */
    public function activePackages(): HasMany
    {
        return $this->hasMany(Package::class)->where('is_available', true);
    }

    /**
     * Scope to get only active cuisines.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
