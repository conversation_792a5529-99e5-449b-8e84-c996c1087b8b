<?php $__env->startSection('title', 'Edit Cuisine'); ?>
<?php $__env->startSection('page-title', 'Edit Cuisine: ' . $cuisine->name); ?>

<?php $__env->startSection('page-actions'); ?>
<div class="d-flex gap-2">
    <a href="<?php echo e(route('admin.cuisines.show', $cuisine)); ?>" class="btn btn-outline-info">
        <i class="bi bi-eye me-1"></i>
        View Cuisine
    </a>
    <a href="<?php echo e(route('admin.cuisines.index')); ?>" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Cuisines
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Cuisine Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo e(route('admin.cuisines.update', $cuisine)); ?>" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Cuisine Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="name" name="name" value="<?php echo e(old('name', $cuisine->name)); ?>" required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="sort_order" name="sort_order" value="<?php echo e(old('sort_order', $cuisine->sort_order)); ?>" min="0">
                                <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <div class="form-text">Lower numbers appear first</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="description" name="description" rows="3"><?php echo e(old('description', $cuisine->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">Cuisine Image</label>
                        <?php if($cuisine->image): ?>
                            <div class="mb-2">
                                <img src="<?php echo e(Storage::url($cuisine->image)); ?>" alt="<?php echo e($cuisine->name); ?>" 
                                     class="rounded" width="100" height="75" style="object-fit: cover;">
                                <div class="form-text">Current image</div>
                            </div>
                        <?php endif; ?>
                        <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="image" name="image" accept="image/*">
                        <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div class="form-text">
                            Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB
                            <?php if($cuisine->image): ?>
                                <br>Leave empty to keep current image
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                   value="1" <?php echo e(old('is_active', $cuisine->is_active) ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                        <div class="form-text">Inactive cuisines won't be visible to customers</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>
                            Update Cuisine
                        </button>
                        <a href="<?php echo e(route('admin.cuisines.show', $cuisine)); ?>" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Cuisine Statistics</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="fs-4 fw-bold text-primary"><?php echo e($cuisine->foodItems()->count()); ?></div>
                            <div class="small text-muted">Food Items</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="fs-4 fw-bold text-success"><?php echo e($cuisine->packages()->count()); ?></div>
                        <div class="small text-muted">Packages</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Cuisine Details</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="row mb-2">
                        <div class="col-5 text-muted">Slug:</div>
                        <div class="col-7"><?php echo e($cuisine->slug); ?></div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5 text-muted">Created:</div>
                        <div class="col-7"><?php echo e($cuisine->created_at->format('M d, Y')); ?></div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5 text-muted">Updated:</div>
                        <div class="col-7"><?php echo e($cuisine->updated_at->format('M d, Y')); ?></div>
                    </div>
                    <div class="row">
                        <div class="col-5 text-muted">Status:</div>
                        <div class="col-7">
                            <span class="badge bg-<?php echo e($cuisine->is_active ? 'success' : 'secondary'); ?>">
                                <?php echo e($cuisine->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Preview</h6>
            </div>
            <div class="card-body">
                <div id="cuisinePreview" class="text-center">
                    <div class="bg-light rounded mb-2" style="height: 120px; display: flex; align-items: center; justify-content: center;">
                        <?php if($cuisine->image): ?>
                            <img src="<?php echo e(Storage::url($cuisine->image)); ?>" alt="<?php echo e($cuisine->name); ?>" 
                                 style="width: 100%; height: 120px; object-fit: cover;" class="rounded">
                        <?php else: ?>
                            <i class="bi bi-image text-muted fs-1"></i>
                        <?php endif; ?>
                    </div>
                    <div class="fw-bold" id="previewName"><?php echo e($cuisine->name); ?></div>
                    <div class="text-muted small" id="previewDescription"><?php echo e($cuisine->description ?: 'No description'); ?></div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Live preview functionality
document.getElementById('name').addEventListener('input', function() {
    document.getElementById('previewName').textContent = this.value || '<?php echo e($cuisine->name); ?>';
});

document.getElementById('description').addEventListener('input', function() {
    document.getElementById('previewDescription').textContent = this.value || 'No description';
});

// Image preview
document.getElementById('image').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.querySelector('#cuisinePreview .bg-light, #cuisinePreview img').parentElement;
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" style="width: 100%; height: 120px; object-fit: cover;" class="rounded">`;
        };
        reader.readAsDataURL(file);
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\r56t7yihu\food-ordering-app\resources\views/admin/cuisines/edit.blade.php ENDPATH**/ ?>