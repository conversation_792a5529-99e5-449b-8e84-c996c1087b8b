<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('food_items', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->text('ingredients')->nullable();
            $table->string('image')->nullable();
            $table->json('images')->nullable(); // Multiple images
            
            // Pricing
            $table->decimal('price_per_unit', 10, 2); // For menu items
            $table->decimal('price_per_kg', 10, 2)->nullable(); // For bulk ordering
            $table->string('unit')->default('piece'); // piece, kg, liter, etc.
            
            // Categories and Cuisine
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->foreignId('cuisine_id')->constrained()->onDelete('cascade');

            // Dietary Information
            $table->boolean('is_vegetarian')->default(false);
            $table->boolean('is_vegan')->default(false);
            $table->boolean('is_gluten_free')->default(false);
            $table->boolean('is_spicy')->default(false);
            $table->enum('spice_level', ['mild', 'medium', 'hot', 'very_hot'])->nullable();
            
            // Availability
            $table->boolean('is_available')->default(true);
            $table->boolean('is_popular')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->boolean('allow_bulk_order')->default(false);
            
            // Nutritional Info
            $table->integer('calories_per_unit')->nullable();
            $table->decimal('protein', 5, 2)->nullable();
            $table->decimal('carbs', 5, 2)->nullable();
            $table->decimal('fat', 5, 2)->nullable();
            
            // Preparation
            $table->integer('preparation_time')->nullable(); // in minutes
            $table->text('preparation_notes')->nullable();
            
            // Ordering
            $table->integer('sort_order')->default(0);
            $table->integer('stock_quantity')->nullable();
            $table->integer('min_order_quantity')->default(1);
            $table->integer('max_order_quantity')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['is_vegetarian', 'is_available']);
            $table->index(['category_id', 'is_available']);
            $table->index(['cuisine_id', 'is_available']);
            $table->index(['is_popular', 'is_available']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('food_items');
    }
};
