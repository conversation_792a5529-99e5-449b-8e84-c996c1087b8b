<?php $__env->startSection('title', 'Home - Food Ordering App'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-mobile">
    <!-- Hero Section -->
    <section class="py-8">
        <div class="bg-gradient-to-r from-orange-500 to-red-500 rounded-lg p-6 text-white">
            <h1 class="text-2xl md:text-3xl font-bold mb-2">Delicious Food Delivered</h1>
            <p class="text-orange-100 mb-4">Order your favorite meals from the comfort of your home</p>
            <a href="<?php echo e(route('menu.index')); ?>" class="bg-white text-orange-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Order Now
            </a>
        </div>
    </section>

    <!-- Catering CTA -->
    <section class="mb-8">
        <div class="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-concierge-bell text-2xl mr-3"></i>
                        <h2 class="text-xl font-bold">Planning an Event?</h2>
                    </div>
                    <p class="text-purple-100 mb-4">Professional catering services for corporate events, weddings, parties, and more!</p>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <a href="<?php echo e(route('catering.index')); ?>" class="bg-white text-purple-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors text-center">
                            View Catering Options
                        </a>
                        <a href="<?php echo e(route('catering.booking')); ?>" class="bg-purple-500 text-white px-6 py-2 rounded-lg font-semibold hover:bg-purple-400 transition-colors text-center border border-purple-400">
                            Get Quote
                        </a>
                    </div>
                </div>
                <div class="hidden md:block ml-6">
                    <i class="fas fa-users text-6xl text-purple-200"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Filters -->
    <section class="mb-8">
        <div class="flex space-x-2 overflow-x-auto pb-2">
            <a href="<?php echo e(route('search', ['vegetarian' => 1])); ?>" class="filter-btn bg-green-100 text-green-800 px-4 py-2 rounded-full whitespace-nowrap">
                <i class="fas fa-leaf mr-1"></i> Vegetarian
            </a>
            <a href="<?php echo e(route('search', ['popular' => 1])); ?>" class="filter-btn bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full whitespace-nowrap">
                <i class="fas fa-star mr-1"></i> Popular
            </a>
            <a href="<?php echo e(route('search', ['order_type' => 'bulk'])); ?>" class="filter-btn bg-blue-100 text-blue-800 px-4 py-2 rounded-full whitespace-nowrap">
                <i class="fas fa-weight mr-1"></i> Bulk Order
            </a>
            <a href="<?php echo e(route('catering.index')); ?>" class="filter-btn bg-purple-100 text-purple-800 px-4 py-2 rounded-full whitespace-nowrap">
                <i class="fas fa-concierge-bell mr-1"></i> Catering
            </a>
        </div>
    </section>

    <!-- Categories -->
    <section class="mb-8">
        <h2 class="text-xl font-bold mb-4">Browse by Category</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="relative group">
                <a href="<?php echo e(route('search', ['category' => $category->id])); ?>" class="bg-white rounded-lg p-4 text-center shadow-sm hover:shadow-md transition-shadow block">
                    <?php if($category->image): ?>
                        <img src="<?php echo e($category->image); ?>" alt="<?php echo e($category->name); ?>" class="w-12 h-12 mx-auto mb-2 rounded-full object-cover">
                    <?php else: ?>
                        <div class="w-12 h-12 mx-auto mb-2 bg-orange-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-utensils text-orange-600"></i>
                        </div>
                    <?php endif; ?>
                    <h3 class="font-semibold text-sm"><?php echo e($category->name); ?></h3>
                    <?php if($category->subcategories->count() > 0): ?>
                        <span class="text-xs text-gray-500 mt-1 block"><?php echo e($category->subcategories->count()); ?> subcategories</span>
                    <?php endif; ?>
                </a>

                <!-- Subcategories Dropdown -->
                <?php if($category->subcategories->count() > 0): ?>
                <div class="absolute top-full left-0 right-0 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10 mt-1">
                    <div class="p-2">
                        <div class="text-xs font-semibold text-gray-700 mb-2 px-2"><?php echo e($category->name); ?> Subcategories:</div>
                        <?php $__currentLoopData = $category->subcategories->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(route('search', ['category' => $category->id, 'subcategory' => $subcategory->id])); ?>"
                               class="block px-2 py-1 text-xs text-gray-600 hover:bg-orange-50 hover:text-orange-600 rounded transition-colors">
                                <?php echo e($subcategory->name); ?>

                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php if($category->subcategories->count() > 5): ?>
                            <a href="<?php echo e(route('search', ['category' => $category->id])); ?>"
                               class="block px-2 py-1 text-xs text-orange-600 font-medium hover:bg-orange-50 rounded transition-colors">
                                View all <?php echo e($category->subcategories->count()); ?> subcategories →
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </section>

    <!-- Cuisines -->
    <section class="mb-8">
        <h2 class="text-xl font-bold mb-4">Explore Cuisines</h2>
        <div class="flex space-x-4 overflow-x-auto pb-2">
            <?php $__currentLoopData = $cuisines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cuisine): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <a href="<?php echo e(route('search', ['cuisine' => $cuisine->id])); ?>" class="bg-white rounded-lg p-4 text-center shadow-sm hover:shadow-md transition-shadow min-w-[120px]">
                <?php if($cuisine->image): ?>
                    <img src="<?php echo e($cuisine->image); ?>" alt="<?php echo e($cuisine->name); ?>" class="w-16 h-16 mx-auto mb-2 rounded-full object-cover">
                <?php else: ?>
                    <div class="w-16 h-16 mx-auto mb-2 bg-orange-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-globe text-orange-600"></i>
                    </div>
                <?php endif; ?>
                <h3 class="font-semibold text-sm"><?php echo e($cuisine->name); ?></h3>
            </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </section>

    <!-- Featured Packages -->
    <?php if($featuredPackages->count() > 0): ?>
    <section class="mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">Featured Packages</h2>
            <a href="<?php echo e(route('menu.index')); ?>" class="text-orange-600 text-sm font-semibold">View All</a>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <?php $__currentLoopData = $featuredPackages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="relative">
                    <?php if($package->image): ?>
                        <img src="<?php echo e($package->image); ?>" alt="<?php echo e($package->name); ?>" class="w-full h-32 object-cover">
                    <?php else: ?>
                        <div class="w-full h-32 bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-image text-gray-400 text-2xl"></i>
                        </div>
                    <?php endif; ?>
                    <?php if($package->is_popular): ?>
                        <span class="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-semibold">
                            <i class="fas fa-star mr-1"></i>Popular
                        </span>
                    <?php endif; ?>
                    <?php if($package->discount_percentage > 0): ?>
                        <span class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                            <?php echo e($package->discount_percentage); ?>% OFF
                        </span>
                    <?php endif; ?>
                </div>
                <div class="p-4">
                    <h3 class="font-semibold mb-1"><?php echo e($package->name); ?></h3>
                    <p class="text-gray-600 text-sm mb-2 line-clamp-2"><?php echo e($package->description); ?></p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-orange-600">$<?php echo e(number_format($package->price, 2)); ?></span>
                            <?php if($package->original_price && $package->original_price > $package->price): ?>
                                <span class="text-sm text-gray-500 line-through">$<?php echo e(number_format($package->original_price, 2)); ?></span>
                            <?php endif; ?>
                        </div>
                        <button onclick="addToCart('package', <?php echo e($package->id); ?>)" class="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700 transition-colors">
                            Add to Cart
                        </button>
                    </div>
                    <?php if($package->serves_people): ?>
                        <p class="text-xs text-gray-500 mt-1">Serves <?php echo e($package->serves_people); ?> people</p>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </section>
    <?php endif; ?>

    <!-- Popular Food Items -->
    <?php if($popularFoodItems->count() > 0): ?>
    <section class="mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">Popular Items</h2>
            <a href="<?php echo e(route('search', ['popular' => 1])); ?>" class="text-orange-600 text-sm font-semibold">View All</a>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <?php $__currentLoopData = $popularFoodItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="relative">
                    <?php if($item->image): ?>
                        <img src="<?php echo e($item->image); ?>" alt="<?php echo e($item->name); ?>" class="w-full h-24 object-cover">
                    <?php else: ?>
                        <div class="w-full h-24 bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-image text-gray-400"></i>
                        </div>
                    <?php endif; ?>
                    <?php if($item->is_vegetarian): ?>
                        <span class="absolute top-1 left-1 bg-green-500 text-white px-1 py-0.5 rounded text-xs">
                            <i class="fas fa-leaf"></i>
                        </span>
                    <?php endif; ?>
                    <?php if($item->is_spicy): ?>
                        <span class="absolute top-1 right-1 bg-red-500 text-white px-1 py-0.5 rounded text-xs">
                            <i class="fas fa-pepper-hot"></i>
                        </span>
                    <?php endif; ?>
                </div>
                <div class="p-3">
                    <h3 class="font-semibold text-sm mb-1"><?php echo e($item->name); ?></h3>
                    <p class="text-xs text-gray-600 mb-2"><?php echo e($item->category->name); ?></p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-bold text-orange-600">$<?php echo e(number_format($item->price_per_unit, 2)); ?></span>
                        <button onclick="addToCart('food_item', <?php echo e($item->id); ?>)" class="bg-orange-600 text-white px-2 py-1 rounded text-xs hover:bg-orange-700 transition-colors">
                            Add
                        </button>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </section>
    <?php endif; ?>

    <!-- Featured Food Items -->
    <?php if($featuredFoodItems->count() > 0): ?>
    <section class="mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">Featured Items</h2>
            <a href="<?php echo e(route('menu.index')); ?>" class="text-orange-600 text-sm font-semibold">View All</a>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <?php $__currentLoopData = $featuredFoodItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="relative">
                    <?php if($item->image): ?>
                        <img src="<?php echo e($item->image); ?>" alt="<?php echo e($item->name); ?>" class="w-full h-24 object-cover">
                    <?php else: ?>
                        <div class="w-full h-24 bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-image text-gray-400"></i>
                        </div>
                    <?php endif; ?>
                    <?php if($item->is_vegetarian): ?>
                        <span class="absolute top-1 left-1 bg-green-500 text-white px-1 py-0.5 rounded text-xs">
                            <i class="fas fa-leaf"></i>
                        </span>
                    <?php endif; ?>
                    <?php if($item->is_spicy): ?>
                        <span class="absolute top-1 right-1 bg-red-500 text-white px-1 py-0.5 rounded text-xs">
                            <i class="fas fa-pepper-hot"></i>
                        </span>
                    <?php endif; ?>
                </div>
                <div class="p-3">
                    <h3 class="font-semibold text-sm mb-1"><?php echo e($item->name); ?></h3>
                    <p class="text-xs text-gray-600 mb-2"><?php echo e($item->category->name); ?></p>
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-sm font-bold text-orange-600">$<?php echo e(number_format($item->price_per_unit, 2)); ?></span>
                            <?php if($item->allow_bulk_order && $item->price_per_kg): ?>
                                <span class="text-xs text-gray-500 block">$<?php echo e(number_format($item->price_per_kg, 2)); ?>/kg</span>
                            <?php endif; ?>
                        </div>
                        <button onclick="addToCart('food_item', <?php echo e($item->id); ?>)" class="bg-orange-600 text-white px-2 py-1 rounded text-xs hover:bg-orange-700 transition-colors">
                            Add
                        </button>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </section>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\r56t7yihu\food-ordering-app\resources\views/home.blade.php ENDPATH**/ ?>