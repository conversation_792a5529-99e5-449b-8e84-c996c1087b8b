@extends('admin.layouts.app')

@section('title', 'User Details')
@section('page-title', 'User Details')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Users
    </a>
    <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
        <i class="bi bi-pencil me-1"></i>
        Edit User
    </a>
    @if($user->id !== auth()->id())
        <form method="POST" action="{{ route('admin.users.toggle-status', $user) }}" class="d-inline">
            @csrf
            @method('PATCH')
            <button type="submit" class="btn btn-{{ $user->is_active ? 'warning' : 'success' }}">
                <i class="bi bi-{{ $user->is_active ? 'pause' : 'play' }} me-1"></i>
                {{ $user->is_active ? 'Deactivate' : 'Activate' }}
            </button>
        </form>
    @endif
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">User Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Full Name</label>
                            <p class="form-control-plaintext">{{ $user->name }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Email Address</label>
                            <p class="form-control-plaintext">{{ $user->email }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Phone Number</label>
                            <p class="form-control-plaintext">{{ $user->phone ?? 'Not provided' }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Role</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-{{ $user->role === 'super_admin' ? 'danger' : ($user->role === 'admin' ? 'warning' : 'info') }} fs-6">
                                    {{ ucfirst(str_replace('_', ' ', $user->role)) }}
                                </span>
                            </p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Status</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }} fs-6">
                                    {{ $user->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Last Login</label>
                            <p class="form-control-plaintext">
                                @if($user->last_login_at)
                                    {{ $user->last_login_at->format('M d, Y H:i A') }}
                                    <small class="text-muted">({{ $user->last_login_at->diffForHumans() }})</small>
                                @else
                                    <span class="text-muted">Never logged in</span>
                                @endif
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Member Since</label>
                            <p class="form-control-plaintext">
                                {{ $user->created_at->format('M d, Y') }}
                                <small class="text-muted">({{ $user->created_at->diffForHumans() }})</small>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Last Updated</label>
                            <p class="form-control-plaintext">
                                {{ $user->updated_at->format('M d, Y H:i A') }}
                                <small class="text-muted">({{ $user->updated_at->diffForHumans() }})</small>
                            </p>
                        </div>
                    </div>
                </div>
                
                @if($user->address)
                    <div class="mb-3">
                        <label class="form-label fw-bold">Address</label>
                        <p class="form-control-plaintext">{{ $user->address }}</p>
                    </div>
                @endif
            </div>
        </div>
        
        @if($user->role === 'customer')
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Order History</h5>
                </div>
                <div class="card-body">
                    @if($user->orders && $user->orders->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Date</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                        <th>Payment</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->orders->take(10) as $order)
                                        <tr>
                                            <td>{{ $order->order_number }}</td>
                                            <td>{{ $order->created_at->format('M d, Y') }}</td>
                                            <td>${{ number_format($order->total_amount, 2) }}</td>
                                            <td>
                                                <span class="badge bg-{{ $order->status === 'completed' ? 'success' : ($order->status === 'cancelled' ? 'danger' : 'warning') }}">
                                                    {{ ucfirst($order->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $order->payment_status === 'paid' ? 'success' : 'warning' }}">
                                                    {{ ucfirst($order->payment_status) }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.orders.show', $order) }}" class="btn btn-sm btn-outline-primary">
                                                    View
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        @if($user->orders->count() > 10)
                            <div class="text-center mt-3">
                                <a href="{{ route('admin.orders.index', ['user' => $user->id]) }}" class="btn btn-outline-primary">
                                    View All Orders ({{ $user->orders->count() }})
                                </a>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-4">
                            <i class="bi bi-receipt fs-1 text-muted"></i>
                            <p class="text-muted mt-2">No orders placed yet</p>
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Quick Stats</h6>
            </div>
            <div class="card-body">
                @if($user->role === 'customer')
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-0">{{ $user->orders ? $user->orders->count() : 0 }}</h4>
                                <small class="text-muted">Total Orders</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success mb-0">${{ $user->orders ? number_format($user->orders->sum('total_amount'), 2) : '0.00' }}</h4>
                            <small class="text-muted">Total Spent</small>
                        </div>
                    </div>
                @else
                    <div class="text-center">
                        <i class="bi bi-shield-check fs-1 text-primary"></i>
                        <p class="text-muted mt-2">Administrative User</p>
                    </div>
                @endif
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Account Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-outline-primary">
                        <i class="bi bi-pencil me-1"></i>
                        Edit User
                    </a>
                    
                    @if($user->id !== auth()->id())
                        <form method="POST" action="{{ route('admin.users.toggle-status', $user) }}">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-outline-{{ $user->is_active ? 'warning' : 'success' }} w-100">
                                <i class="bi bi-{{ $user->is_active ? 'pause' : 'play' }} me-1"></i>
                                {{ $user->is_active ? 'Deactivate User' : 'Activate User' }}
                            </button>
                        </form>
                        
                        @if(!$user->isSuperAdmin())
                            <form method="POST" action="{{ route('admin.users.destroy', $user) }}" 
                                  onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger w-100">
                                    <i class="bi bi-trash me-1"></i>
                                    Delete User
                                </button>
                            </form>
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
