<?php $__env->startSection('title', 'Reports & Analytics'); ?>
<?php $__env->startSection('page-title', 'Reports & Analytics'); ?>

<?php $__env->startSection('page-actions'); ?>
<div class="btn-group" role="group">
    <a href="<?php echo e(route('admin.reports.sales')); ?>" class="btn btn-outline-primary">
        <i class="bi bi-graph-up me-1"></i>
        Sales Report
    </a>
    <a href="<?php echo e(route('admin.reports.customers')); ?>" class="btn btn-outline-info">
        <i class="bi bi-people me-1"></i>
        Customer Report
    </a>
    <a href="<?php echo e(route('admin.reports.inventory')); ?>" class="btn btn-outline-warning">
        <i class="bi bi-box me-1"></i>
        Inventory Report
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Revenue</div>
                        <div class="h5 mb-0 font-weight-bold">$<?php echo e(number_format($totalRevenue, 2)); ?></div>
                        <?php if($revenueGrowth != 0): ?>
                            <small class="text-<?php echo e($revenueGrowth > 0 ? 'success' : 'danger'); ?>">
                                <i class="bi bi-arrow-<?php echo e($revenueGrowth > 0 ? 'up' : 'down'); ?>"></i>
                                <?php echo e(abs(round($revenueGrowth, 1))); ?>% from last month
                            </small>
                        <?php endif; ?>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Orders</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo e(number_format($totalOrders)); ?></div>
                        <?php if($orderGrowth != 0): ?>
                            <small class="text-<?php echo e($orderGrowth > 0 ? 'success' : 'danger'); ?>">
                                <i class="bi bi-arrow-<?php echo e($orderGrowth > 0 ? 'up' : 'down'); ?>"></i>
                                <?php echo e(abs(round($orderGrowth, 1))); ?>% from last month
                            </small>
                        <?php endif; ?>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-receipt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Customers</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo e(number_format($totalCustomers)); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Food Items</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo e(number_format($totalFoodItems)); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-egg-fried fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Categories -->
<div class="row">
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    Sales Reports
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted">Analyze sales performance, revenue trends, and payment methods.</p>
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('admin.reports.sales')); ?>" class="btn btn-outline-primary">
                        <i class="bi bi-bar-chart me-1"></i>
                        View Sales Report
                    </a>
                    <a href="<?php echo e(route('admin.reports.export-sales')); ?>" class="btn btn-outline-success">
                        <i class="bi bi-download me-1"></i>
                        Export Sales Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-people me-2"></i>
                    Customer Reports
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted">Track customer acquisition, retention, and top customers.</p>
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('admin.reports.customers')); ?>" class="btn btn-outline-info">
                        <i class="bi bi-person-lines-fill me-1"></i>
                        View Customer Report
                    </a>
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-people me-1"></i>
                        Manage Users
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-box me-2"></i>
                    Inventory Reports
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted">Monitor stock levels, popular items, and category performance.</p>
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('admin.reports.inventory')); ?>" class="btn btn-outline-warning">
                        <i class="bi bi-boxes me-1"></i>
                        View Inventory Report
                    </a>
                    <a href="<?php echo e(route('admin.food-items.index')); ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-egg-fried me-1"></i>
                        Manage Food Items
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Performing Items -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Top Performing Food Items</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Item</th>
                                <th>Orders</th>
                                <th>Revenue</th>
                                <th>Avg. Order Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $topFoodItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($item->image): ?>
                                                <img src="<?php echo e(Storage::url($item->image)); ?>" alt="<?php echo e($item->name); ?>" 
                                                     class="rounded me-3" style="width: 40px; height: 40px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="bi bi-image text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="fw-bold"><?php echo e($item->name); ?></div>
                                                <small class="text-muted"><?php echo e($item->category->name ?? 'N/A'); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo e($item->order_items_count); ?></span>
                                    </td>
                                    <td>
                                        <span class="fw-bold">$<?php echo e(number_format($item->order_items_sum_total_price ?? 0, 2)); ?></span>
                                    </td>
                                    <td>
                                        <?php if($item->order_items_count > 0): ?>
                                            $<?php echo e(number_format(($item->order_items_sum_total_price ?? 0) / $item->order_items_count, 2)); ?>

                                        <?php else: ?>
                                            $0.00
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <div class="text-muted">No data available</div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Recent Orders -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Recent Regular Orders</h6>
            </div>
            <div class="card-body">
                <?php $__empty_1 = true; $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="fw-bold"><?php echo e($order->order_number); ?></div>
                            <small class="text-muted"><?php echo e($order->customer_name); ?></small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">$<?php echo e(number_format($order->total_amount, 2)); ?></div>
                            <small class="text-muted"><?php echo e($order->created_at->format('M d')); ?></small>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center text-muted">No recent orders</div>
                <?php endif; ?>
                
                <div class="text-center mt-3">
                    <a href="<?php echo e(route('admin.orders.index')); ?>" class="btn btn-sm btn-outline-primary">
                        View All Orders
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Catering Orders -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Recent Catering Orders</h6>
            </div>
            <div class="card-body">
                <?php $__empty_1 = true; $__currentLoopData = $recentCateringOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="fw-bold"><?php echo e($order->order_number); ?></div>
                            <small class="text-muted"><?php echo e($order->customer_name); ?></small>
                            <br><small class="text-info"><?php echo e($order->eventType->name ?? 'N/A'); ?></small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">$<?php echo e(number_format($order->total_amount, 2)); ?></div>
                            <small class="text-muted"><?php echo e($order->created_at->format('M d')); ?></small>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center text-muted">No recent catering orders</div>
                <?php endif; ?>
                
                <div class="text-center mt-3">
                    <a href="<?php echo e(route('admin.catering-orders.index')); ?>" class="btn btn-sm btn-outline-primary">
                        View All Catering Orders
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\r56t7yihu\food-ordering-app\resources\views/admin/reports/index.blade.php ENDPATH**/ ?>