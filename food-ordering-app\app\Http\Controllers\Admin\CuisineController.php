<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Cuisine;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CuisineController extends Controller
{
    /**
     * Display a listing of cuisines.
     */
    public function index(Request $request)
    {
        $query = Cuisine::query()->withCount('foodItems');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $cuisines = $query->ordered()->paginate(15);

        return view('admin.cuisines.index', compact('cuisines'));
    }

    /**
     * Show the form for creating a new cuisine.
     */
    public function create()
    {
        return view('admin.cuisines.create');
    }

    /**
     * Store a newly created cuisine in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:cuisines',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Generate slug
        $validated['slug'] = Str::slug($validated['name']);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('cuisines', 'public');
        }

        // Handle boolean field
        $validated['is_active'] = $request->has('is_active');

        Cuisine::create($validated);

        return redirect()->route('admin.cuisines.index')
            ->with('success', 'Cuisine created successfully.');
    }

    /**
     * Display the specified cuisine.
     */
    public function show(Cuisine $cuisine)
    {
        $cuisine->load(['foodItems', 'packages']);
        return view('admin.cuisines.show', compact('cuisine'));
    }

    /**
     * Show the form for editing the specified cuisine.
     */
    public function edit(Cuisine $cuisine)
    {
        return view('admin.cuisines.edit', compact('cuisine'));
    }

    /**
     * Update the specified cuisine in storage.
     */
    public function update(Request $request, Cuisine $cuisine)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:cuisines,name,' . $cuisine->id,
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Update slug if name changed
        if ($validated['name'] !== $cuisine->name) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($cuisine->image) {
                Storage::disk('public')->delete($cuisine->image);
            }
            $validated['image'] = $request->file('image')->store('cuisines', 'public');
        }

        // Handle boolean field
        $validated['is_active'] = $request->has('is_active');

        $cuisine->update($validated);

        return redirect()->route('admin.cuisines.index')
            ->with('success', 'Cuisine updated successfully.');
    }

    /**
     * Remove the specified cuisine from storage.
     */
    public function destroy(Cuisine $cuisine)
    {
        // Check if cuisine has associated food items or packages
        if ($cuisine->foodItems()->count() > 0 || $cuisine->packages()->count() > 0) {
            return redirect()->route('admin.cuisines.index')
                ->with('error', 'Cannot delete cuisine that has associated food items or packages.');
        }

        // Delete image
        if ($cuisine->image) {
            Storage::disk('public')->delete($cuisine->image);
        }

        $cuisine->delete();

        return redirect()->route('admin.cuisines.index')
            ->with('success', 'Cuisine deleted successfully.');
    }

    /**
     * Toggle cuisine status.
     */
    public function toggleStatus(Cuisine $cuisine)
    {
        $cuisine->update(['is_active' => !$cuisine->is_active]);

        $status = $cuisine->is_active ? 'activated' : 'deactivated';
        return redirect()->back()
            ->with('success', "Cuisine {$status} successfully.");
    }

    /**
     * Bulk actions for cuisines.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,activate,deactivate',
            'items' => 'required|array',
            'items.*' => 'exists:cuisines,id',
        ]);

        $cuisines = Cuisine::whereIn('id', $request->items);

        switch ($request->action) {
            case 'delete':
                // Check if any cuisine has associated items
                $hasAssociatedItems = $cuisines->whereHas('foodItems')
                    ->orWhereHas('packages')
                    ->exists();

                if ($hasAssociatedItems) {
                    return redirect()->back()
                        ->with('error', 'Cannot delete cuisines that have associated food items or packages.');
                }

                $cuisines->each(function ($cuisine) {
                    if ($cuisine->image) {
                        Storage::disk('public')->delete($cuisine->image);
                    }
                });
                $cuisines->delete();
                $message = 'Selected cuisines deleted successfully.';
                break;
            case 'activate':
                $cuisines->update(['is_active' => true]);
                $message = 'Selected cuisines activated successfully.';
                break;
            case 'deactivate':
                $cuisines->update(['is_active' => false]);
                $message = 'Selected cuisines deactivated successfully.';
                break;
        }

        return redirect()->back()->with('success', $message);
    }
}
