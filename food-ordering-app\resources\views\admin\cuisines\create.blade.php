@extends('admin.layouts.app')

@section('title', 'Create Cuisine')
@section('page-title', 'Create New Cuisine')

@section('page-actions')
<a href="{{ route('admin.cuisines.index') }}" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-1"></i>
    Back to Cuisines
</a>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Cuisine Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.cuisines.store') }}" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Cuisine Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Lower numbers appear first</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">Cuisine Image</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                   value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                        <div class="form-text">Inactive cuisines won't be visible to customers</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>
                            Create Cuisine
                        </button>
                        <a href="{{ route('admin.cuisines.index') }}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Cuisine Guidelines</h6>
            </div>
            <div class="card-body">
                <div class="small text-muted">
                    <h6>Naming Guidelines:</h6>
                    <ul class="mb-3">
                        <li>Use authentic cuisine names</li>
                        <li>Keep names concise (under 50 characters)</li>
                        <li>Avoid special characters</li>
                        <li>Use proper case (e.g., "Italian", "Chinese")</li>
                    </ul>
                    
                    <h6>Image Guidelines:</h6>
                    <ul class="mb-3">
                        <li>Recommended size: 400x300 pixels</li>
                        <li>Use representative cuisine images</li>
                        <li>Ensure good lighting and contrast</li>
                        <li>Avoid text overlays</li>
                    </ul>
                    
                    <h6>Organization Tips:</h6>
                    <ul class="mb-0">
                        <li>Use sort order to control display sequence</li>
                        <li>Group similar cuisines logically</li>
                        <li>Consider customer preferences</li>
                        <li>Review and update regularly</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Preview</h6>
            </div>
            <div class="card-body">
                <div id="cuisinePreview" class="text-center">
                    <div class="bg-light rounded mb-2" style="height: 120px; display: flex; align-items: center; justify-content: center;">
                        <i class="bi bi-image text-muted fs-1"></i>
                    </div>
                    <div class="fw-bold" id="previewName">Cuisine Name</div>
                    <div class="text-muted small" id="previewDescription">Cuisine description will appear here</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Live preview functionality
document.getElementById('name').addEventListener('input', function() {
    document.getElementById('previewName').textContent = this.value || 'Cuisine Name';
});

document.getElementById('description').addEventListener('input', function() {
    document.getElementById('previewDescription').textContent = this.value || 'Cuisine description will appear here';
});

// Image preview
document.getElementById('image').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.querySelector('#cuisinePreview .bg-light');
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" style="width: 100%; height: 120px; object-fit: cover;" class="rounded">`;
        };
        reader.readAsDataURL(file);
    }
});
</script>
@endpush
