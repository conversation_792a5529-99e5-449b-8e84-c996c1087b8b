<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    /**
     * Display a listing of categories.
     */
    public function index(Request $request)
    {
        $query = Category::query()->withCount('foodItems')->with('parent');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Order hierarchically: main categories first, then their subcategories
        $categories = $query->orderByRaw('COALESCE(parent_id, id), parent_id IS NULL DESC, sort_order, name')->paginate(15);

        return view('admin.categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new category.
     */
    public function create()
    {
        $mainCategories = Category::active()->mainCategories()->ordered()->get();
        return view('admin.categories.create', compact('mainCategories'));
    }

    /**
     * Store a newly created category in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:categories',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'parent_id' => 'nullable|exists:categories,id',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Generate slug
        $validated['slug'] = Str::slug($validated['name']);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('categories', 'public');
        }

        // Handle boolean field
        $validated['is_active'] = $request->has('is_active');

        Category::create($validated);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Category created successfully.');
    }

    /**
     * Display the specified category.
     */
    public function show(Category $category)
    {
        $category->load(['foodItems', 'packages']);
        return view('admin.categories.show', compact('category'));
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit(Category $category)
    {
        $mainCategories = Category::active()->mainCategories()->ordered()->get();
        return view('admin.categories.edit', compact('category', 'mainCategories'));
    }

    /**
     * Update the specified category in storage.
     */
    public function update(Request $request, Category $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:categories,name,' . $category->id,
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'parent_id' => [
                'nullable',
                'exists:categories,id',
                function ($attribute, $value, $fail) use ($category) {
                    // Prevent setting self as parent
                    if ($value == $category->id) {
                        $fail('A category cannot be its own parent.');
                    }
                    // Prevent circular references (setting a child as parent)
                    if ($value && $category->allSubcategories()->where('id', $value)->exists()) {
                        $fail('Cannot set a subcategory as parent of its parent category.');
                    }
                },
            ],
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Update slug if name changed
        if ($validated['name'] !== $category->name) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($category->image) {
                Storage::disk('public')->delete($category->image);
            }
            $validated['image'] = $request->file('image')->store('categories', 'public');
        }

        // Handle boolean field
        $validated['is_active'] = $request->has('is_active');

        $category->update($validated);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified category from storage.
     */
    public function destroy(Category $category)
    {
        // Check if category has associated food items or packages
        if ($category->foodItems()->count() > 0 || $category->packages()->count() > 0) {
            return redirect()->route('admin.categories.index')
                ->with('error', 'Cannot delete category that has associated food items or packages.');
        }

        // Delete image
        if ($category->image) {
            Storage::disk('public')->delete($category->image);
        }

        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('success', 'Category deleted successfully.');
    }

    /**
     * Toggle category status.
     */
    public function toggleStatus(Category $category)
    {
        $category->update(['is_active' => !$category->is_active]);

        $status = $category->is_active ? 'activated' : 'deactivated';
        return redirect()->back()
            ->with('success', "Category {$status} successfully.");
    }

    /**
     * Bulk actions for categories.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,activate,deactivate',
            'items' => 'required|array',
            'items.*' => 'exists:categories,id',
        ]);

        $categories = Category::whereIn('id', $request->items);

        switch ($request->action) {
            case 'delete':
                // Check if any category has associated items
                $hasAssociatedItems = $categories->whereHas('foodItems')
                    ->orWhereHas('packages')
                    ->exists();

                if ($hasAssociatedItems) {
                    return redirect()->back()
                        ->with('error', 'Cannot delete categories that have associated food items or packages.');
                }

                $categories->each(function ($category) {
                    if ($category->image) {
                        Storage::disk('public')->delete($category->image);
                    }
                });
                $categories->delete();
                $message = 'Selected categories deleted successfully.';
                break;
            case 'activate':
                $categories->update(['is_active' => true]);
                $message = 'Selected categories activated successfully.';
                break;
            case 'deactivate':
                $categories->update(['is_active' => false]);
                $message = 'Selected categories deactivated successfully.';
                break;
        }

        return redirect()->back()->with('success', $message);
    }
}
