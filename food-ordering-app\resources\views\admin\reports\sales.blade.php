@extends('admin.layouts.app')

@section('title', 'Sales Report')
@section('page-title', 'Sales Report')

@section('page-actions')
<div class="btn-group" role="group">
    <a href="{{ route('admin.reports.index') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Reports
    </a>
    <a href="{{ route('admin.reports.export-sales', request()->query()) }}" class="btn btn-outline-success">
        <i class="bi bi-download me-1"></i>
        Export Data
    </a>
</div>
@endsection

@section('content')
<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">Report Filters</h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.reports.sales') }}">
            <div class="row">
                <div class="col-md-3">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ $dateFrom }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ $dateTo }}">
                </div>
                <div class="col-md-3">
                    <label for="group_by" class="form-label">Group By</label>
                    <select class="form-select" id="group_by" name="group_by">
                        <option value="day" {{ $groupBy === 'day' ? 'selected' : '' }}>Daily</option>
                        <option value="week" {{ $groupBy === 'week' ? 'selected' : '' }}>Weekly</option>
                        <option value="month" {{ $groupBy === 'month' ? 'selected' : '' }}>Monthly</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-funnel me-1"></i>
                        Apply Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Sales Chart -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Sales Trend</h5>
    </div>
    <div class="card-body">
        <canvas id="salesChart" height="100"></canvas>
    </div>
</div>

<div class="row">
    <!-- Category Performance -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Category Performance</h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Category</th>
                                <th>Orders</th>
                                <th>Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($categoryPerformance as $category)
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ $category->name }}</div>
                                        @if($category->description)
                                            <small class="text-muted">{{ Str::limit($category->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $category->food_items_order_items_count ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <span class="fw-bold">${{ number_format($category->food_items_order_items_sum_total_price ?? 0, 2) }}</span>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="3" class="text-center py-4">
                                        <div class="text-muted">No data available for selected period</div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Payment Methods</h6>
            </div>
            <div class="card-body">
                <canvas id="paymentChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Order Types -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Order Types</h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Order Type</th>
                                <th>Count</th>
                                <th>Revenue</th>
                                <th>Avg. Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($orderTypes as $type)
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $type->order_type)) }}</span>
                                    </td>
                                    <td>{{ $type->count }}</td>
                                    <td>${{ number_format($type->revenue, 2) }}</td>
                                    <td>${{ number_format($type->revenue / $type->count, 2) }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <div class="text-muted">No data available</div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Stats -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Period Summary</h6>
            </div>
            <div class="card-body">
                @php
                    $totalRevenue = $salesData->sum('revenue');
                    $totalOrders = $salesData->sum('order_count');
                    $avgOrderValue = $totalOrders > 0 ? $totalRevenue / $totalOrders : 0;
                    $daysInPeriod = \Carbon\Carbon::parse($dateFrom)->diffInDays(\Carbon\Carbon::parse($dateTo)) + 1;
                    $avgDailyRevenue = $daysInPeriod > 0 ? $totalRevenue / $daysInPeriod : 0;
                @endphp
                
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-0">${{ number_format($totalRevenue, 2) }}</h4>
                            <small class="text-muted">Total Revenue</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-0">{{ number_format($totalOrders) }}</h4>
                        <small class="text-muted">Total Orders</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-info mb-0">${{ number_format($avgOrderValue, 2) }}</h5>
                            <small class="text-muted">Avg. Order Value</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-warning mb-0">${{ number_format($avgDailyRevenue, 2) }}</h5>
                        <small class="text-muted">Avg. Daily Revenue</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Sales Trend Chart
const salesCtx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(salesCtx, {
    type: 'line',
    data: {
        labels: [
            @foreach($salesData as $data)
                '{{ $data->period }}',
            @endforeach
        ],
        datasets: [{
            label: 'Revenue',
            data: [
                @foreach($salesData as $data)
                    {{ $data->revenue }},
                @endforeach
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            yAxisID: 'y'
        }, {
            label: 'Orders',
            data: [
                @foreach($salesData as $data)
                    {{ $data->order_count }},
                @endforeach
            ],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            x: {
                display: true,
                title: {
                    display: true,
                    text: 'Period'
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Revenue ($)'
                },
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Orders'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});

// Payment Methods Chart
const paymentCtx = document.getElementById('paymentChart').getContext('2d');
const paymentChart = new Chart(paymentCtx, {
    type: 'doughnut',
    data: {
        labels: [
            @foreach($paymentMethods as $method)
                '{{ ucfirst($method->payment_method) }}',
            @endforeach
        ],
        datasets: [{
            data: [
                @foreach($paymentMethods as $method)
                    {{ $method->revenue }},
                @endforeach
            ],
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': $' + context.parsed.toLocaleString();
                    }
                }
            }
        }
    }
});
</script>
@endpush
