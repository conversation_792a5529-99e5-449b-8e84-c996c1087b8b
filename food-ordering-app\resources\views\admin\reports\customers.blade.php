@extends('admin.layouts.app')

@section('title', 'Customer Report')
@section('page-title', 'Customer Report')

@section('page-actions')
<div class="btn-group" role="group">
    <a href="{{ route('admin.reports.index') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Reports
    </a>
    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary">
        <i class="bi bi-people me-1"></i>
        Manage Users
    </a>
</div>
@endsection

@section('content')
<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">Report Filters</h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.reports.customers') }}">
            <div class="row">
                <div class="col-md-4">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ $dateFrom }}">
                </div>
                <div class="col-md-4">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ $dateTo }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-funnel me-1"></i>
                        Apply Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card-info">
            <div class="card-body text-center">
                <h3 class="text-primary">{{ $newCustomers }}</h3>
                <p class="text-muted mb-0">New Customers</p>
                <small class="text-muted">in selected period</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card-success">
            <div class="card-body text-center">
                <h3 class="text-success">{{ $topCustomersByOrders->count() }}</h3>
                <p class="text-muted mb-0">Active Customers</p>
                <small class="text-muted">with orders</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card-warning">
            <div class="card-body text-center">
                <h3 class="text-warning">${{ number_format($topCustomersByRevenue->sum('orders_sum_total_amount'), 2) }}</h3>
                <p class="text-muted mb-0">Total Revenue</p>
                <small class="text-muted">from customers</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                @php
                    $avgRevenue = $topCustomersByRevenue->count() > 0 ? $topCustomersByRevenue->sum('orders_sum_total_amount') / $topCustomersByRevenue->count() : 0;
                @endphp
                <h3 class="text-info">${{ number_format($avgRevenue, 2) }}</h3>
                <p class="text-muted mb-0">Avg. Customer Value</p>
                <small class="text-muted">per customer</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Top Customers by Orders -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Top Customers by Order Count</h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Customer</th>
                                <th>Orders</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($topCustomersByOrders as $customer)
                                <tr>
                                    <td>
                                        <div>
                                            <div class="fw-bold">{{ $customer->name }}</div>
                                            <small class="text-muted">{{ $customer->email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $customer->orders_count }}</span>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.users.show', $customer) }}" class="btn btn-sm btn-outline-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="3" class="text-center py-4">
                                        <div class="text-muted">No data available for selected period</div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers by Revenue -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Top Customers by Revenue</h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Customer</th>
                                <th>Revenue</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($topCustomersByRevenue as $customer)
                                <tr>
                                    <td>
                                        <div>
                                            <div class="fw-bold">{{ $customer->name }}</div>
                                            <small class="text-muted">{{ $customer->email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-success">${{ number_format($customer->orders_sum_total_amount ?? 0, 2) }}</span>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.users.show', $customer) }}" class="btn btn-sm btn-outline-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="3" class="text-center py-4">
                                        <div class="text-muted">No data available for selected period</div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customer Registration Trends -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">Customer Registration Trends</h6>
    </div>
    <div class="card-body">
        <canvas id="registrationChart" height="100"></canvas>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Customer Registration Chart
const registrationCtx = document.getElementById('registrationChart').getContext('2d');
const registrationChart = new Chart(registrationCtx, {
    type: 'line',
    data: {
        labels: [
            @foreach($registrationTrends as $trend)
                '{{ \Carbon\Carbon::parse($trend->date)->format('M d') }}',
            @endforeach
        ],
        datasets: [{
            label: 'New Registrations',
            data: [
                @foreach($registrationTrends as $trend)
                    {{ $trend->count }},
                @endforeach
            ],
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        scales: {
            x: {
                display: true,
                title: {
                    display: true,
                    text: 'Date'
                }
            },
            y: {
                display: true,
                title: {
                    display: true,
                    text: 'New Customers'
                },
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        }
    }
});
</script>
@endpush
