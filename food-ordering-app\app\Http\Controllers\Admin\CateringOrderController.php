<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CateringOrder;
use App\Models\CateringEventType;
use App\Models\CateringPackage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CateringOrderController extends Controller
{
    /**
     * Display a listing of catering orders.
     */
    public function index(Request $request)
    {
        $query = CateringOrder::with(['eventType', 'package', 'user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%")
                  ->orWhere('customer_phone', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by event type
        if ($request->filled('event_type_id')) {
            $query->where('catering_event_type_id', $request->event_type_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('event_date', '>=', $request->date_from);
        }
        
        if ($request->filled('date_to')) {
            $query->where('event_date', '<=', $request->date_to);
        }

        $cateringOrders = $query->latest()->paginate(15);

        // Get status counts for filter badges
        $statusCounts = CateringOrder::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status');

        // Get event types for filter
        $eventTypes = CateringEventType::where('is_active', true)->get();

        return view('admin.catering-orders.index', compact('cateringOrders', 'statusCounts', 'eventTypes'));
    }

    /**
     * Display the specified catering order.
     */
    public function show(CateringOrder $cateringOrder)
    {
        $cateringOrder->load(['eventType', 'package', 'orderItems.foodItem', 'user']);
        
        return view('admin.catering-orders.show', compact('cateringOrder'));
    }

    /**
     * Update the order status.
     */
    public function updateStatus(Request $request, CateringOrder $cateringOrder)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,in_preparation,ready,in_transit,delivered,completed,cancelled',
            'notes' => 'nullable|string|max:1000',
        ]);

        $oldStatus = $cateringOrder->status;
        $cateringOrder->update([
            'status' => $request->status,
            'notes' => $request->notes ? $cateringOrder->notes . "\n" . now()->format('Y-m-d H:i') . ": " . $request->notes : $cateringOrder->notes,
        ]);

        // Log status change (you can implement activity logging later if needed)

        return redirect()->back()->with('success', 'Order status updated successfully.');
    }

    /**
     * Update the payment status.
     */
    public function updatePaymentStatus(Request $request, CateringOrder $cateringOrder)
    {
        $request->validate([
            'payment_status' => 'required|in:pending,paid,failed,refunded',
        ]);

        $oldPaymentStatus = $cateringOrder->payment_status;
        $cateringOrder->update([
            'payment_status' => $request->payment_status,
        ]);

        // Log payment status change (you can implement activity logging later if needed)

        return redirect()->back()->with('success', 'Payment status updated successfully.');
    }

    /**
     * Generate invoice for catering order.
     */
    public function invoice(CateringOrder $cateringOrder)
    {
        $cateringOrder->load(['eventType', 'package', 'orderItems.foodItem']);
        
        return view('admin.catering-orders.invoice', compact('cateringOrder'));
    }

    /**
     * Export catering orders to CSV.
     */
    public function export(Request $request)
    {
        $query = CateringOrder::with(['eventType', 'package', 'user']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('event_type_id')) {
            $query->where('catering_event_type_id', $request->event_type_id);
        }

        if ($request->filled('date_from')) {
            $query->where('event_date', '>=', $request->date_from);
        }
        
        if ($request->filled('date_to')) {
            $query->where('event_date', '<=', $request->date_to);
        }

        $orders = $query->latest()->get();

        $filename = 'catering_orders_' . now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($orders) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Order Number',
                'Customer Name',
                'Customer Email',
                'Customer Phone',
                'Event Type',
                'Event Date',
                'Guest Count',
                'Package',
                'Total Amount',
                'Status',
                'Payment Status',
                'Created At'
            ]);

            // CSV data
            foreach ($orders as $order) {
                fputcsv($file, [
                    $order->order_number,
                    $order->customer_name,
                    $order->customer_email,
                    $order->customer_phone,
                    $order->eventType->name ?? 'N/A',
                    $order->event_date->format('Y-m-d'),
                    $order->guest_count,
                    $order->package->name ?? 'N/A',
                    $order->total_amount,
                    $order->status,
                    $order->payment_status,
                    $order->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get catering order statistics.
     */
    public function statistics()
    {
        // Monthly revenue for catering orders
        $monthlyRevenue = CateringOrder::where('payment_status', 'paid')
            ->where('created_at', '>=', now()->subMonths(12))
            ->select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('SUM(total_amount) as revenue'),
                DB::raw('COUNT(*) as order_count')
            )
            ->groupBy('year', 'month')
            ->orderBy('year', 'asc')
            ->orderBy('month', 'asc')
            ->get();

        // Status distribution
        $statusDistribution = CateringOrder::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();

        // Event type popularity
        $eventTypeStats = CateringOrder::with('eventType')
            ->select('catering_event_type_id', DB::raw('count(*) as count'), DB::raw('SUM(total_amount) as revenue'))
            ->groupBy('catering_event_type_id')
            ->orderBy('count', 'desc')
            ->get();

        // Recent orders
        $recentOrders = CateringOrder::with(['eventType', 'package'])
            ->latest()
            ->limit(10)
            ->get();

        return view('admin.catering-orders.statistics', compact(
            'monthlyRevenue',
            'statusDistribution',
            'eventTypeStats',
            'recentOrders'
        ));
    }
}
