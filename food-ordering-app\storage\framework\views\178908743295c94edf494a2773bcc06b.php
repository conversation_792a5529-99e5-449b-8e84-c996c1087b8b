<?php $__env->startSection('title', 'Edit Food Item'); ?>
<?php $__env->startSection('page-title', 'Edit Food Item'); ?>

<?php $__env->startSection('page-actions'); ?>
<div class="btn-group">
    <a href="<?php echo e(route('admin.food-items.index')); ?>" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Food Items
    </a>
    <a href="<?php echo e(route('admin.food-items.show', $foodItem)); ?>" class="btn btn-outline-info">
        <i class="bi bi-eye me-1"></i>
        View Food Item
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Edit Food Item Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo e(route('admin.food-items.update', $foodItem)); ?>" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Food Item Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="name" name="name" value="<?php echo e(old('name', $foodItem->name)); ?>" required>
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="image" class="form-label">Food Image</label>
                            <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="image" name="image" accept="image/*">
                            <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <small class="text-muted">Leave empty to keep current image. Max size: 2MB</small>
                        </div>
                    </div>
                    
                    <?php if($foodItem->image): ?>
                        <div class="mb-3">
                            <label class="form-label">Current Image</label>
                            <div>
                                <img src="<?php echo e(Storage::url($foodItem->image)); ?>" alt="<?php echo e($foodItem->name); ?>" 
                                     class="rounded" style="width: 150px; height: 150px; object-fit: cover;">
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="description" name="description" rows="4" required><?php echo e(old('description', $foodItem->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="ingredients" class="form-label">Ingredients</label>
                        <textarea class="form-control <?php $__errorArgs = ['ingredients'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="ingredients" name="ingredients" rows="3"><?php echo e(old('ingredients', $foodItem->ingredients)); ?></textarea>
                        <?php $__errorArgs = ['ingredients'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <small class="text-muted">List main ingredients separated by commas</small>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="price_per_unit" class="form-label">Price per Unit <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control <?php $__errorArgs = ['price_per_unit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="price_per_unit" name="price_per_unit" value="<?php echo e(old('price_per_unit', $foodItem->price_per_unit)); ?>" 
                                       step="0.01" min="0" required>
                                <?php $__errorArgs = ['price_per_unit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="price_per_kg" class="form-label">Price per KG</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control <?php $__errorArgs = ['price_per_kg'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="price_per_kg" name="price_per_kg" value="<?php echo e(old('price_per_kg', $foodItem->price_per_kg)); ?>" 
                                       step="0.01" min="0">
                                <?php $__errorArgs = ['price_per_kg'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="unit" class="form-label">Unit <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['unit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="unit" name="unit" required>
                                <option value="">Select Unit</option>
                                <option value="piece" <?php echo e(old('unit', $foodItem->unit) === 'piece' ? 'selected' : ''); ?>>Piece</option>
                                <option value="plate" <?php echo e(old('unit', $foodItem->unit) === 'plate' ? 'selected' : ''); ?>>Plate</option>
                                <option value="bowl" <?php echo e(old('unit', $foodItem->unit) === 'bowl' ? 'selected' : ''); ?>>Bowl</option>
                                <option value="cup" <?php echo e(old('unit', $foodItem->unit) === 'cup' ? 'selected' : ''); ?>>Cup</option>
                                <option value="kg" <?php echo e(old('unit', $foodItem->unit) === 'kg' ? 'selected' : ''); ?>>Kilogram</option>
                                <option value="gram" <?php echo e(old('unit', $foodItem->unit) === 'gram' ? 'selected' : ''); ?>>Gram</option>
                                <option value="liter" <?php echo e(old('unit', $foodItem->unit) === 'liter' ? 'selected' : ''); ?>>Liter</option>
                                <option value="ml" <?php echo e(old('unit', $foodItem->unit) === 'ml' ? 'selected' : ''); ?>>Milliliter</option>
                            </select>
                            <?php $__errorArgs = ['unit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id); ?>" <?php echo e(old('category_id', $foodItem->category_id) == $category->id ? 'selected' : ''); ?>>
                                        <?php echo e($category->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="cuisine_id" class="form-label">Cuisine <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['cuisine_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="cuisine_id" name="cuisine_id" required>
                                <option value="">Select Cuisine</option>
                                <?php $__currentLoopData = $cuisines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cuisine): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($cuisine->id); ?>" <?php echo e(old('cuisine_id', $foodItem->cuisine_id) == $cuisine->id ? 'selected' : ''); ?>>
                                        <?php echo e($cuisine->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['cuisine_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="stock_quantity" class="form-label">Stock Quantity</label>
                            <input type="number" class="form-control <?php $__errorArgs = ['stock_quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="stock_quantity" name="stock_quantity" value="<?php echo e(old('stock_quantity', $foodItem->stock_quantity)); ?>" min="0">
                            <?php $__errorArgs = ['stock_quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <small class="text-muted">Leave empty for unlimited stock</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="spice_level" class="form-label">Spice Level</label>
                            <select class="form-select <?php $__errorArgs = ['spice_level'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="spice_level" name="spice_level">
                                <option value="">Not Applicable</option>
                                <option value="1" <?php echo e(old('spice_level', $foodItem->spice_level) == '1' ? 'selected' : ''); ?>>1 - Mild</option>
                                <option value="2" <?php echo e(old('spice_level', $foodItem->spice_level) == '2' ? 'selected' : ''); ?>>2 - Medium</option>
                                <option value="3" <?php echo e(old('spice_level', $foodItem->spice_level) == '3' ? 'selected' : ''); ?>>3 - Hot</option>
                                <option value="4" <?php echo e(old('spice_level', $foodItem->spice_level) == '4' ? 'selected' : ''); ?>>4 - Very Hot</option>
                                <option value="5" <?php echo e(old('spice_level', $foodItem->spice_level) == '5' ? 'selected' : ''); ?>>5 - Extremely Hot</option>
                            </select>
                            <?php $__errorArgs = ['spice_level'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Dietary Information</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_vegetarian" name="is_vegetarian" 
                                       <?php echo e(old('is_vegetarian', $foodItem->is_vegetarian) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_vegetarian">Vegetarian</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_vegan" name="is_vegan" 
                                       <?php echo e(old('is_vegan', $foodItem->is_vegan) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_vegan">Vegan</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_gluten_free" name="is_gluten_free" 
                                       <?php echo e(old('is_gluten_free', $foodItem->is_gluten_free) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_gluten_free">Gluten Free</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_spicy" name="is_spicy" 
                                       <?php echo e(old('is_spicy', $foodItem->is_spicy) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_spicy">Spicy</label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>Status & Features</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_available" name="is_available" 
                                       <?php echo e(old('is_available', $foodItem->is_available) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_available">Available for Order</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_popular" name="is_popular" 
                                       <?php echo e(old('is_popular', $foodItem->is_popular) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_popular">Popular Item</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                       <?php echo e(old('is_featured', $foodItem->is_featured) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_featured">Featured Item</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="allow_bulk_order" name="allow_bulk_order" 
                                       <?php echo e(old('allow_bulk_order', $foodItem->allow_bulk_order) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="allow_bulk_order">Allow Bulk Orders</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>
                            Update Food Item
                        </button>
                        <a href="<?php echo e(route('admin.food-items.show', $foodItem)); ?>" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Current Food Item</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <?php if($foodItem->image): ?>
                        <img src="<?php echo e(Storage::url($foodItem->image)); ?>" alt="<?php echo e($foodItem->name); ?>" 
                             class="rounded" style="width: 120px; height: 120px; object-fit: cover;">
                    <?php else: ?>
                        <div class="bg-light rounded d-inline-flex align-items-center justify-content-center" 
                             style="width: 120px; height: 120px;">
                            <i class="bi bi-image text-muted fs-3"></i>
                        </div>
                    <?php endif; ?>
                    <h6 class="mt-2 mb-0"><?php echo e($foodItem->name); ?></h6>
                    <small class="text-muted">$<?php echo e(number_format($foodItem->price_per_unit, 2)); ?> per <?php echo e($foodItem->unit); ?></small>
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <small class="text-muted">Status</small>
                            <div>
                                <span class="badge bg-<?php echo e($foodItem->is_available ? 'success' : 'secondary'); ?>">
                                    <?php echo e($foodItem->is_available ? 'Available' : 'Unavailable'); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Category</small>
                        <div>
                            <span class="badge bg-info">
                                <?php echo e($foodItem->category->name ?? 'None'); ?>

                            </span>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="small">
                    <div class="mb-2">
                        <strong>Created:</strong><br>
                        <?php echo e($foodItem->created_at->format('M d, Y')); ?>

                    </div>
                    <div class="mb-2">
                        <strong>Last Updated:</strong><br>
                        <?php echo e($foodItem->updated_at->format('M d, Y H:i A')); ?>

                    </div>
                    <div>
                        <strong>Total Orders:</strong><br>
                        <?php echo e($foodItem->orderItems ? $foodItem->orderItems->count() : 0); ?>

                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Edit Guidelines</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">Important Notes</h6>
                    <ul class="mb-0 small">
                        <li>Changes will be reflected immediately on the website</li>
                        <li>Price changes affect new orders only</li>
                        <li>Image changes may take a few minutes to appear</li>
                        <li>Availability changes affect ordering immediately</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\r56t7yihu\food-ordering-app\resources\views/admin/food-items/edit.blade.php ENDPATH**/ ?>