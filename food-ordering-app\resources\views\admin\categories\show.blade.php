@extends('admin.layouts.app')

@section('title', 'Category Details')
@section('page-title', 'Category: ' . $category->name)

@section('page-actions')
<div class="d-flex gap-2">
    <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-primary">
        <i class="bi bi-pencil me-1"></i>
        Edit Category
    </a>
    <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to Categories
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8">
        <!-- Category Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Category Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        @if($category->image)
                            <img src="{{ Storage::url($category->image) }}" alt="{{ $category->name }}" 
                                 class="img-fluid rounded">
                        @else
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                 style="height: 200px;">
                                <i class="bi bi-image text-muted fs-1"></i>
                            </div>
                        @endif
                    </div>
                    <div class="col-md-8">
                        <h4>{{ $category->name }}</h4>
                        <p class="text-muted mb-3">{{ $category->description ?: 'No description available' }}</p>
                        
                        <div class="row">
                            <div class="col-sm-6">
                                <strong>Slug:</strong> {{ $category->slug }}<br>
                                <strong>Sort Order:</strong> {{ $category->sort_order ?? 'Not set' }}<br>
                                <strong>Status:</strong> 
                                <span class="badge bg-{{ $category->is_active ? 'success' : 'secondary' }}">
                                    {{ $category->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                            <div class="col-sm-6">
                                <strong>Created:</strong> {{ $category->created_at->format('M d, Y g:i A') }}<br>
                                <strong>Updated:</strong> {{ $category->updated_at->format('M d, Y g:i A') }}<br>
                                <strong>Food Items:</strong> {{ $category->foodItems->count() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Food Items in this Category -->
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Food Items ({{ $category->foodItems->count() }})</h5>
                <a href="{{ route('admin.food-items.create', ['category_id' => $category->id]) }}" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-plus me-1"></i>
                    Add Food Item
                </a>
            </div>
            <div class="card-body p-0">
                @if($category->foodItems->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Stock</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($category->foodItems as $item)
                                    <tr>
                                        <td>
                                            @if($item->image)
                                                <img src="{{ Storage::url($item->image) }}" alt="{{ $item->name }}" 
                                                     class="rounded" width="40" height="40" style="object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="bi bi-image text-muted"></i>
                                                </div>
                                            @endif
                                        </td>
                                        <td>
                                            <div>
                                                <div class="fw-bold">{{ $item->name }}</div>
                                                <small class="text-muted">{{ Str::limit($item->description, 40) }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="fw-bold">${{ number_format($item->price_per_unit, 2) }}</div>
                                            <small class="text-muted">per {{ $item->unit }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $item->is_available ? 'success' : 'secondary' }}">
                                                {{ $item->is_available ? 'Available' : 'Unavailable' }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($item->stock_quantity !== null)
                                                <span class="badge bg-{{ $item->stock_quantity > 10 ? 'success' : ($item->stock_quantity > 0 ? 'warning' : 'danger') }}">
                                                    {{ $item->stock_quantity }}
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ route('admin.food-items.show', $item) }}" class="btn btn-outline-info" title="View">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.food-items.edit', $item) }}" class="btn btn-outline-primary" title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <div class="text-muted">
                            <i class="bi bi-egg-fried fs-1 d-block mb-2"></i>
                            No food items in this category yet
                        </div>
                        <a href="{{ route('admin.food-items.create', ['category_id' => $category->id]) }}" class="btn btn-primary">
                            <i class="bi bi-plus me-1"></i>
                            Add First Food Item
                        </a>
                    </div>
                @endif
            </div>
        </div>
        
        <!-- Packages in this Category -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Packages ({{ $category->packages->count() }})</h5>
            </div>
            <div class="card-body p-0">
                @if($category->packages->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Price</th>
                                    <th>Items</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($category->packages as $package)
                                    <tr>
                                        <td>
                                            <div>
                                                <div class="fw-bold">{{ $package->name }}</div>
                                                <small class="text-muted">{{ Str::limit($package->description, 50) }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="fw-bold">${{ number_format($package->price, 2) }}</div>
                                            <small class="text-muted">per package</small>
                                        </td>
                                        <td>{{ $package->packageItems->count() }} items</td>
                                        <td>
                                            <span class="badge bg-{{ $package->is_active ? 'success' : 'secondary' }}">
                                                {{ $package->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="#" class="btn btn-outline-info" title="View">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="#" class="btn btn-outline-primary" title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <div class="text-muted">
                            <i class="bi bi-box fs-1 d-block mb-2"></i>
                            No packages in this category yet
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Quick Statistics</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="fs-4 fw-bold text-primary">{{ $category->foodItems->count() }}</div>
                            <div class="small text-muted">Food Items</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="fs-4 fw-bold text-success">{{ $category->packages->count() }}</div>
                        <div class="small text-muted">Packages</div>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="fs-4 fw-bold text-info">{{ $category->foodItems->where('is_available', true)->count() }}</div>
                            <div class="small text-muted">Available Items</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="fs-4 fw-bold text-warning">{{ $category->foodItems->where('is_featured', true)->count() }}</div>
                        <div class="small text-muted">Featured Items</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-pencil me-1"></i>
                        Edit Category
                    </a>
                    <a href="{{ route('admin.food-items.create', ['category_id' => $category->id]) }}" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-plus me-1"></i>
                        Add Food Item
                    </a>
                    <form method="POST" action="{{ route('admin.categories.toggle-status', $category) }}" class="d-inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="btn btn-outline-{{ $category->is_active ? 'warning' : 'success' }} btn-sm w-100">
                            <i class="bi bi-{{ $category->is_active ? 'pause' : 'play' }} me-1"></i>
                            {{ $category->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
