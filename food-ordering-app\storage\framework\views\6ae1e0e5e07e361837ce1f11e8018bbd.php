<?php $__env->startSection('title', 'Categories Management'); ?>
<?php $__env->startSection('page-title', 'Categories Management'); ?>

<?php $__env->startSection('page-actions'); ?>
<a href="<?php echo e(route('admin.categories.create')); ?>" class="btn btn-primary">
    <i class="bi bi-plus-circle me-1"></i>
    Add New Category
</a>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">All Categories</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="<?php echo e(route('admin.categories.index')); ?>" class="d-flex gap-2">
                    <input type="text" name="search" class="form-control form-control-sm" 
                           placeholder="Search categories..." value="<?php echo e(request('search')); ?>">
                    
                    <select name="status" class="form-select form-select-sm">
                        <option value="">All Status</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                    
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-search"></i>
                    </button>
                    
                    <?php if(request()->hasAny(['search', 'status'])): ?>
                        <a href="<?php echo e(route('admin.categories.index')); ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-x-circle"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bulk Actions -->
    <div class="card-body border-bottom">
        <form id="bulkActionForm" method="POST" action="<?php echo e(route('admin.categories.bulk-action')); ?>">
            <?php echo csrf_field(); ?>
            <div class="row align-items-center">
                <div class="col-auto">
                    <input type="checkbox" id="selectAll" class="form-check-input">
                    <label for="selectAll" class="form-check-label">Select All</label>
                </div>
                <div class="col-auto">
                    <select name="action" class="form-select form-select-sm" required>
                        <option value="">Bulk Actions</option>
                        <option value="activate">Activate</option>
                        <option value="deactivate">Deactivate</option>
                        <option value="delete">Delete</option>
                    </select>
                </div>
                <div class="col-auto">
                    <button type="submit" class="btn btn-outline-primary btn-sm">Apply</button>
                </div>
            </div>
        </form>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="40">
                            <input type="checkbox" class="form-check-input" id="selectAllHeader">
                        </th>
                        <th>Image</th>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Description</th>
                        <th>Food Items</th>
                        <th>Sort Order</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <input type="checkbox" name="items[]" value="<?php echo e($category->id); ?>" class="form-check-input item-checkbox">
                            </td>
                            <td>
                                <?php if($category->image): ?>
                                    <img src="<?php echo e(Storage::url($category->image)); ?>" alt="<?php echo e($category->name); ?>" 
                                         class="rounded" width="50" height="50" style="object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                         style="width: 50px; height: 50px;">
                                        <i class="bi bi-image text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold"><?php echo e($category->name); ?></div>
                                    <small class="text-muted"><?php echo e($category->slug); ?></small>
                                </div>
                            </td>
                            <td>
                                <div><?php echo e(Str::limit($category->description, 60) ?: '-'); ?></div>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo e($category->food_items_count ?? 0); ?></span>
                            </td>
                            <td><?php echo e($category->sort_order ?? '-'); ?></td>
                            <td>
                                <span class="badge bg-<?php echo e($category->is_active ? 'success' : 'secondary'); ?>">
                                    <?php echo e($category->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="<?php echo e(route('admin.categories.show', $category)); ?>" class="btn btn-outline-info" title="View">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.categories.edit', $category)); ?>" class="btn btn-outline-primary" title="Edit">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    
                                    <form method="POST" action="<?php echo e(route('admin.categories.toggle-status', $category)); ?>" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('PATCH'); ?>
                                        <button type="submit" class="btn btn-outline-<?php echo e($category->is_active ? 'warning' : 'success'); ?>" 
                                                title="<?php echo e($category->is_active ? 'Deactivate' : 'Activate'); ?>">
                                            <i class="bi bi-<?php echo e($category->is_active ? 'pause' : 'play'); ?>"></i>
                                        </button>
                                    </form>
                                    
                                    <form method="POST" action="<?php echo e(route('admin.categories.destroy', $category)); ?>" class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this category?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-outline-danger" title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-grid-3x3-gap fs-1 d-block mb-2"></i>
                                    No categories found
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <?php if($categories->hasPages()): ?>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Showing <?php echo e($categories->firstItem()); ?> to <?php echo e($categories->lastItem()); ?> of <?php echo e($categories->total()); ?> results
                </div>
                <?php echo e($categories->links()); ?>

            </div>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Select All functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

document.getElementById('selectAllHeader').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Bulk action form submission
document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
    const selectedItems = document.querySelectorAll('.item-checkbox:checked');
    if (selectedItems.length === 0) {
        e.preventDefault();
        alert('Please select at least one item.');
        return;
    }
    
    const action = document.querySelector('select[name="action"]').value;
    if (!action) {
        e.preventDefault();
        alert('Please select an action.');
        return;
    }
    
    if (action === 'delete') {
        if (!confirm('Are you sure you want to delete the selected categories?')) {
            e.preventDefault();
        }
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\r56t7yihu\food-ordering-app\resources\views/admin/categories/index.blade.php ENDPATH**/ ?>