<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MealTime;

class MealTimeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $mealTimes = [
            [
                'name' => 'Breakfast',
                'slug' => 'breakfast',
                'description' => 'Morning meal typically served between 6 AM to 11 AM',
                'icon' => 'fas fa-coffee',
                'start_time' => '06:00',
                'end_time' => '11:00',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Lunch',
                'slug' => 'lunch',
                'description' => 'Midday meal typically served between 11 AM to 4 PM',
                'icon' => 'fas fa-utensils',
                'start_time' => '11:00',
                'end_time' => '16:00',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Tea/Snacks',
                'slug' => 'tea-snacks',
                'description' => 'Light refreshments and snacks typically served between 3 PM to 6 PM',
                'icon' => 'fas fa-cookie-bite',
                'start_time' => '15:00',
                'end_time' => '18:00',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Dinner',
                'slug' => 'dinner',
                'description' => 'Evening meal typically served between 6 PM to 11 PM',
                'icon' => 'fas fa-moon',
                'start_time' => '18:00',
                'end_time' => '23:00',
                'is_active' => true,
                'sort_order' => 4,
            ],
        ];

        foreach ($mealTimes as $mealTime) {
            MealTime::updateOrCreate(
                ['slug' => $mealTime['slug']],
                $mealTime
            );
        }
    }
}
