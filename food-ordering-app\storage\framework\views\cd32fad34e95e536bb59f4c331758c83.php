<?php $__env->startSection('title', 'Catering Orders Management'); ?>
<?php $__env->startSection('page-title', 'Catering Orders Management'); ?>

<?php $__env->startSection('page-actions'); ?>
<div class="btn-group" role="group">
    <a href="<?php echo e(route('admin.catering-orders.export')); ?><?php echo e(request()->getQueryString() ? '?' . request()->getQueryString() : ''); ?>" 
       class="btn btn-outline-success">
        <i class="bi bi-download me-1"></i>
        Export
    </a>
    <a href="<?php echo e(route('admin.catering-orders.statistics')); ?>" class="btn btn-outline-info">
        <i class="bi bi-bar-chart me-1"></i>
        Statistics
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Status Filter Badges -->
<div class="row mb-3">
    <div class="col">
        <div class="d-flex gap-2 flex-wrap">
            <a href="<?php echo e(route('admin.catering-orders.index')); ?>" 
               class="btn btn-outline-primary <?php echo e(!request('status') ? 'active' : ''); ?>">
                All Orders
                <span class="badge bg-primary ms-1"><?php echo e(array_sum($statusCounts->toArray())); ?></span>
            </a>
            <?php $__currentLoopData = ['pending', 'confirmed', 'in_preparation', 'ready', 'in_transit', 'delivered', 'completed', 'cancelled']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('admin.catering-orders.index', ['status' => $status])); ?>" 
                   class="btn btn-outline-<?php echo e($status === 'completed' || $status === 'delivered' ? 'success' : ($status === 'cancelled' ? 'danger' : 'warning')); ?> <?php echo e(request('status') === $status ? 'active' : ''); ?>">
                    <?php echo e(ucfirst(str_replace('_', ' ', $status))); ?>

                    <span class="badge bg-<?php echo e($status === 'completed' || $status === 'delivered' ? 'success' : ($status === 'cancelled' ? 'danger' : 'warning')); ?> ms-1">
                        <?php echo e($statusCounts[$status] ?? 0); ?>

                    </span>
                </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">All Catering Orders</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="<?php echo e(route('admin.catering-orders.index')); ?>" class="d-flex gap-2">
                    <input type="text" name="search" class="form-control form-control-sm" 
                           placeholder="Search orders..." value="<?php echo e(request('search')); ?>">
                    
                    <select name="event_type_id" class="form-select form-select-sm">
                        <option value="">All Event Types</option>
                        <?php $__currentLoopData = $eventTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $eventType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($eventType->id); ?>" <?php echo e(request('event_type_id') == $eventType->id ? 'selected' : ''); ?>>
                                <?php echo e($eventType->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    
                    <input type="date" name="date_from" class="form-control form-control-sm" 
                           value="<?php echo e(request('date_from')); ?>" placeholder="From Date">
                    
                    <input type="date" name="date_to" class="form-control form-control-sm" 
                           value="<?php echo e(request('date_to')); ?>" placeholder="To Date">
                    
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-search"></i>
                    </button>
                    
                    <?php if(request()->hasAny(['search', 'event_type_id', 'date_from', 'date_to'])): ?>
                        <a href="<?php echo e(route('admin.catering-orders.index')); ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-x-circle"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Order #</th>
                        <th>Customer</th>
                        <th>Event</th>
                        <th>Event Date</th>
                        <th>Guests</th>
                        <th>Amount</th>
                        <th>Payment</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $cateringOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="fw-bold"><?php echo e($order->order_number); ?></div>
                                <?php if($order->notes): ?>
                                    <small class="text-muted">
                                        <i class="bi bi-chat-left-text"></i>
                                        <?php echo e(Str::limit($order->notes, 30)); ?>

                                    </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold"><?php echo e($order->customer_name); ?></div>
                                    <small class="text-muted"><?php echo e($order->customer_email); ?></small>
                                    <?php if($order->customer_phone): ?>
                                        <br><small class="text-muted"><?php echo e($order->customer_phone); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold"><?php echo e($order->eventType->name ?? 'N/A'); ?></div>
                                    <?php if($order->package): ?>
                                        <small class="text-muted"><?php echo e($order->package->name); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div><?php echo e($order->event_date->format('M d, Y')); ?></div>
                                <?php if($order->event_time): ?>
                                    <small class="text-muted"><?php echo e($order->event_time); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo e($order->guest_count); ?> guests</span>
                            </td>
                            <td>
                                <div class="fw-bold">$<?php echo e(number_format($order->total_amount, 2)); ?></div>
                                <?php if($order->deposit_amount > 0): ?>
                                    <small class="text-muted">
                                        Deposit: $<?php echo e(number_format($order->deposit_amount, 2)); ?>

                                    </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($order->payment_status === 'paid' ? 'success' : ($order->payment_status === 'failed' ? 'danger' : 'warning')); ?>">
                                    <?php echo e(ucfirst($order->payment_status)); ?>

                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($order->status === 'completed' || $order->status === 'delivered' ? 'success' : ($order->status === 'cancelled' ? 'danger' : 'warning')); ?>">
                                    <?php echo e(ucfirst(str_replace('_', ' ', $order->status))); ?>

                                </span>
                            </td>
                            <td>
                                <div><?php echo e($order->created_at->format('M d, Y')); ?></div>
                                <small class="text-muted"><?php echo e($order->created_at->format('H:i')); ?></small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="<?php echo e(route('admin.catering-orders.show', $order)); ?>" class="btn btn-outline-info" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.catering-orders.invoice', $order)); ?>" class="btn btn-outline-secondary" title="Print Invoice" target="_blank">
                                        <i class="bi bi-printer"></i>
                                    </a>
                                    <?php if(!in_array($order->status, ['completed', 'cancelled'])): ?>
                                        <button type="button" class="btn btn-outline-primary" title="Update Status" 
                                                data-bs-toggle="modal" data-bs-target="#statusModal<?php echo e($order->id); ?>">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>

                        <!-- Status Update Modal -->
                        <div class="modal fade" id="statusModal<?php echo e($order->id); ?>" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form method="POST" action="<?php echo e(route('admin.catering-orders.update-status', $order)); ?>">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('PATCH'); ?>
                                        <div class="modal-header">
                                            <h5 class="modal-title">Update Catering Order Status</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <label class="form-label">Order Number</label>
                                                <input type="text" class="form-control" value="<?php echo e($order->order_number); ?>" readonly>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Current Status</label>
                                                <input type="text" class="form-control" value="<?php echo e(ucfirst(str_replace('_', ' ', $order->status))); ?>" readonly>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">New Status</label>
                                                <select name="status" class="form-select" required>
                                                    <option value="pending" <?php echo e($order->status === 'pending' ? 'selected' : ''); ?>>Pending</option>
                                                    <option value="confirmed" <?php echo e($order->status === 'confirmed' ? 'selected' : ''); ?>>Confirmed</option>
                                                    <option value="in_preparation" <?php echo e($order->status === 'in_preparation' ? 'selected' : ''); ?>>In Preparation</option>
                                                    <option value="ready" <?php echo e($order->status === 'ready' ? 'selected' : ''); ?>>Ready</option>
                                                    <option value="in_transit" <?php echo e($order->status === 'in_transit' ? 'selected' : ''); ?>>In Transit</option>
                                                    <option value="delivered" <?php echo e($order->status === 'delivered' ? 'selected' : ''); ?>>Delivered</option>
                                                    <option value="completed" <?php echo e($order->status === 'completed' ? 'selected' : ''); ?>>Completed</option>
                                                    <option value="cancelled" <?php echo e($order->status === 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Notes (Optional)</label>
                                                <textarea name="notes" class="form-control" rows="3" placeholder="Add any notes about this status update..."></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-primary">Update Status</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="10" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-calendar-event fs-1 d-block mb-2"></i>
                                    No catering orders found
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <?php if($cateringOrders->hasPages()): ?>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Showing <?php echo e($cateringOrders->firstItem()); ?> to <?php echo e($cateringOrders->lastItem()); ?> of <?php echo e($cateringOrders->total()); ?> results
                </div>
                <?php echo e($cateringOrders->links()); ?>

            </div>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\r56t7yihu\food-ordering-app\resources\views/admin/catering-orders/index.blade.php ENDPATH**/ ?>