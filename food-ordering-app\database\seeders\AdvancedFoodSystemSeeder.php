<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\FoodItem;
use App\Models\Category;
use App\Models\Cuisine;
use App\Models\MealTime;

class AdvancedFoodSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First run the MealTimeSeeder
        $this->call(MealTimeSeeder::class);

        // Get some sample data
        $categories = Category::take(5)->get();
        $cuisines = Cuisine::take(5)->get();
        $mealTimes = MealTime::all();

        if ($categories->isEmpty() || $cuisines->isEmpty()) {
            $this->command->info('Please ensure categories and cuisines are seeded first.');
            return;
        }

        // Create sample food items with the new structure
        $sampleFoodItems = [
            [
                'name' => 'Masala Dosa',
                'slug' => 'masala-dosa',
                'description' => 'Crispy South Indian crepe filled with spiced potato filling',
                'food_type' => 'veg',
                'price_per_unit' => 120.00,
                'is_available' => true,
                'is_popular' => true,
                'categories' => [1, 2], // Breakfast, South Indian
                'cuisines' => [1], // South Indian
                'meal_times' => [1, 2], // Breakfast, Lunch
            ],
            [
                'name' => 'Chicken Biryani',
                'slug' => 'chicken-biryani',
                'description' => 'Aromatic basmati rice cooked with tender chicken and spices',
                'food_type' => 'non_veg',
                'price_per_unit' => 280.00,
                'is_available' => true,
                'is_popular' => true,
                'categories' => [3, 4], // Main Course, Rice
                'cuisines' => [2, 3], // North Indian, Hyderabadi
                'meal_times' => [2, 4], // Lunch, Dinner
            ],
            [
                'name' => 'Samosa',
                'slug' => 'samosa',
                'description' => 'Crispy fried pastry filled with spiced potatoes and peas',
                'food_type' => 'veg',
                'price_per_unit' => 25.00,
                'is_available' => true,
                'is_featured' => true,
                'categories' => [5], // Snacks
                'cuisines' => [2], // North Indian
                'meal_times' => [3], // Tea/Snacks
            ],
            [
                'name' => 'Butter Chicken',
                'slug' => 'butter-chicken',
                'description' => 'Creamy tomato-based curry with tender chicken pieces',
                'food_type' => 'non_veg',
                'price_per_unit' => 320.00,
                'is_available' => true,
                'is_popular' => true,
                'categories' => [3, 6], // Main Course, Curry
                'cuisines' => [2], // North Indian
                'meal_times' => [2, 4], // Lunch, Dinner
            ],
            [
                'name' => 'Idli Sambhar',
                'slug' => 'idli-sambhar',
                'description' => 'Steamed rice cakes served with lentil curry',
                'food_type' => 'veg',
                'price_per_unit' => 80.00,
                'is_available' => true,
                'categories' => [1, 2], // Breakfast, South Indian
                'cuisines' => [1], // South Indian
                'meal_times' => [1], // Breakfast
            ],
        ];

        foreach ($sampleFoodItems as $itemData) {
            // Extract relationship data
            $categoryIds = $itemData['categories'] ?? [];
            $cuisineIds = $itemData['cuisines'] ?? [];
            $mealTimeIds = $itemData['meal_times'] ?? [];
            
            // Remove relationship data from main array
            unset($itemData['categories'], $itemData['cuisines'], $itemData['meal_times']);

            // Create or update the food item
            $foodItem = FoodItem::updateOrCreate(
                ['slug' => $itemData['slug']],
                $itemData
            );

            // Attach categories
            if (!empty($categoryIds)) {
                $categoryData = [];
                foreach ($categoryIds as $index => $categoryId) {
                    $categoryData[$categoryId] = [
                        'is_primary' => $index === 0, // First category is primary
                        'sort_order' => $index,
                    ];
                }
                $foodItem->categories()->sync($categoryData);
            }

            // Attach cuisines
            if (!empty($cuisineIds)) {
                $cuisineData = [];
                foreach ($cuisineIds as $index => $cuisineId) {
                    $cuisineData[$cuisineId] = [
                        'is_primary' => $index === 0, // First cuisine is primary
                        'sort_order' => $index,
                    ];
                }
                $foodItem->cuisines()->sync($cuisineData);
            }

            // Attach meal times
            if (!empty($mealTimeIds)) {
                $mealTimeData = [];
                foreach ($mealTimeIds as $index => $mealTimeId) {
                    $mealTimeData[$mealTimeId] = [
                        'is_primary' => $index === 0, // First meal time is primary
                        'sort_order' => $index,
                    ];
                }
                $foodItem->mealTimes()->sync($mealTimeData);
            }
        }

        $this->command->info('Advanced food system seeded successfully!');
    }
}
